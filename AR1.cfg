#
# AR1 核心路由器配置
# 负责教学楼、逸夫楼、无线、FW互联
#
sysname AR1
#
# 用户配置
aaa
 local-user admin password irreversible-cipher <PERSON><PERSON><PERSON>@123
 local-user admin privilege level 15
 local-user admin service-type telnet ssh http
#
# SSH配置
ssh server enable
ssh server-source all-interface
#
# VLAN配置
vlan batch 10 20 50 100 101
vlan 10
 description Teaching Building VLAN
#
vlan 20
 description Yifu Building VLAN
#
vlan 50
 description Wireless VLAN
#
vlan 100
 description Management VLAN
#
vlan 101
 description Business VLAN
#
# 接口配置
interface GigabitEthernet0/0/1
 description 连接SW1-教学楼逸夫楼
 port link-type trunk
 port trunk allow-pass vlan 10 20 50 100 101
 undo shutdown
#
interface GigabitEthernet0/0/2
 description 教学楼网关
 ip address ********* *************
 dhcp select interface
 undo shutdown
#
interface GigabitEthernet0/0/3
 description 连接FW
 ip address ********** ***************
 undo shutdown
#
interface GigabitEthernet0/0/4
 description 连接无线AP
 ip address ********* *************
 dhcp select interface
 undo shutdown
#
# VLAN接口配置
interface Vlanif10
 description Teaching Building Gateway
 ip address ********* *************
 dhcp select interface
#
interface Vlanif20
 description Yifu Building Gateway
 ip address ********* *************
 dhcp select interface
#
interface Vlanif50
 description Wireless Gateway
 ip address ********* *************
 dhcp select interface
#
interface Vlanif100
 description Management Gateway
 ip address ********** *************
#
interface Vlanif101
 description Business Gateway
 ip address ********** *************
#
# DHCP配置
dhcp enable
ip pool teaching_pool
 gateway-list *********
 network ********* mask *************
 dns-list ********* *******
 lease day 1
#
ip pool yifu_pool
 gateway-list *********
 network ********* mask *************
 dns-list ********* *******
 lease day 1
#
ip pool wireless_pool
 gateway-list *********
 network ********* mask *************
 dns-list ********* *******
 lease day 1
#
# OSPF配置
ospf 1
 router-id *******
 area 0.0.0.0
  network ********* *********
  network ********* *********
  network ********* *********
  network ********** *******
  network ********** *********
  network ********** *********
#
# 静态路由（备用）
ip route-static 0.0.0.0 0.0.0.0 ********** preference 100
#
# ACL配置 - 禁止教学楼PC访问DMZ区
acl number 3000
 description Block Teaching to DMZ
 rule 5 deny ip source ********* ********* destination ******** *********
 rule 10 permit ip
#
# 应用ACL到连接FW的接口
interface GigabitEthernet0/0/3
 packet-filter 3000 outbound
#
# 管理访问控制
acl number 3001
 description Management Access Control
 rule 5 permit ip source ********** *********
 rule 10 deny ip
#
user-interface vty 0 4
 acl 3001 inbound
 authentication-mode aaa
 user privilege level 15
 idle-timeout 30 0
#
# 日志配置
info-center enable
info-center loghost **********00
info-center source default channel loghost log level warning
#
# NTP配置
ntp-service unicast-server ************
#
# SNMP配置
snmp-agent
snmp-agent local-engineid 800007DB03000000000001
snmp-agent community read public
snmp-agent community write private
snmp-agent sys-info contact "Network Admin"
snmp-agent sys-info location "Core Room AR1"
#
return
