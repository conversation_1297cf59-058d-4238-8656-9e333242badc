#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
eNSP自动化工具测试脚本
验证各个组件的功能是否正常
"""

import os
import json
import tempfile
import unittest
from unittest.mock import patch, MagicMock
import sys

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from ensp_automation import (
    HuaweiDeviceDatabase, TopologyGenerator, ConfigGenerator,
    ENSPDeployer, NetworkTester, NetworkRequirement
)
from enhanced_automation import (
    EnhancedConfigGenerator, SmartInterfaceAllocator,
    HuaweiDeviceInfoFetcher, EnhancedAutomationOrchestrator
)
from config_templates import HuaweiConfigTemplates

class TestHuaweiDeviceDatabase(unittest.TestCase):
    """测试华为设备数据库"""
    
    def setUp(self):
        self.db = HuaweiDeviceDatabase()
    
    def test_get_device_info(self):
        """测试获取设备信息"""
        # 测试已知设备
        ar2220_info = self.db.get_device_info("AR2220")
        self.assertIsNotNone(ar2220_info)
        self.assertEqual(ar2220_info.model, "AR2220")
        self.assertEqual(ar2220_info.type, "router")
        
        # 测试未知设备
        unknown_info = self.db.get_device_info("UNKNOWN_MODEL")
        self.assertIsNone(unknown_info)
    
    def test_get_available_models(self):
        """测试获取可用型号"""
        all_models = self.db.get_available_models()
        self.assertIn("AR2220", all_models)
        self.assertIn("S5700", all_models)
        
        router_models = self.db.get_available_models("router")
        self.assertIn("AR2220", router_models)
        self.assertNotIn("S5700", router_models)

class TestTopologyGenerator(unittest.TestCase):
    """测试拓扑生成器"""
    
    def setUp(self):
        self.db = HuaweiDeviceDatabase()
        self.generator = TopologyGenerator(self.db)
    
    def test_auto_assign_interfaces(self):
        """测试自动接口分配"""
        connections = [
            {
                'src_device': '1',
                'dst_device': '2',
                'src_model': 'AR2220',
                'dst_model': 'S5700'
            }
        ]
        
        assigned = self.generator.auto_assign_interfaces(connections)
        self.assertEqual(len(assigned), 1)
        self.assertIn('src_interface', assigned[0])
        self.assertIn('dst_interface', assigned[0])
        self.assertIn('src_interface_name', assigned[0])
        self.assertIn('dst_interface_name', assigned[0])
    
    def test_generate_topology(self):
        """测试拓扑生成"""
        requirement = NetworkRequirement(
            topology_name="test_topo",
            devices=[
                {"id": "1", "name": "R1", "model": "AR2220"},
                {"id": "2", "name": "SW1", "model": "S5700"}
            ],
            vlans=[{"id": 10, "description": "Test VLAN"}],
            routing_protocol="ospf",
            security_features=["acl"],
            connections=[
                {"src_device": "1", "dst_device": "2", "src_model": "AR2220", "dst_model": "S5700"}
            ]
        )
        
        topo_xml = self.generator.generate_topology(requirement)
        self.assertIn('<?xml version="1.0"', topo_xml)
        self.assertIn('<topo version=', topo_xml)
        self.assertIn('<devices>', topo_xml)
        self.assertIn('<lines>', topo_xml)

class TestConfigTemplates(unittest.TestCase):
    """测试配置模板"""
    
    def setUp(self):
        self.templates = HuaweiConfigTemplates()
    
    def test_get_template(self):
        """测试获取模板"""
        router_template = self.templates.get_template('router_basic')
        self.assertIsNotNone(router_template)
        
        unknown_template = self.templates.get_template('unknown_template')
        self.assertIsNone(unknown_template)
    
    def test_router_config_generation(self):
        """测试路由器配置生成"""
        template = self.templates.get_template('router_basic')
        config_data = {
            'device_name': 'R1',
            'timestamp': '2024-01-01 00:00:00',
            'vlans': [{'id': 10, 'description': 'Test VLAN'}],
            'interfaces': [
                {
                    'name': 'GigabitEthernet0/0/1',
                    'ip': '***********',
                    'mask': '*************',
                    'description': 'Test Interface'
                }
            ],
            'static_routes': [],
            'acls': []
        }
        
        config = template.render(**config_data)
        self.assertIn('sysname R1', config)
        self.assertIn('interface GigabitEthernet0/0/1', config)
        self.assertIn('ip address *********** *************', config)

class TestSmartInterfaceAllocator(unittest.TestCase):
    """测试智能接口分配器"""
    
    def setUp(self):
        self.db = HuaweiDeviceDatabase()
        self.allocator = SmartInterfaceAllocator(self.db)
    
    def test_allocate_interfaces(self):
        """测试接口分配"""
        connections = [
            {
                'src_device': '1',
                'dst_device': '2',
                'src_model': 'AR2220',
                'dst_model': 'S5700',
                'link_type': 'trunk'
            },
            {
                'src_device': '1',
                'dst_device': '3',
                'src_model': 'AR2220',
                'dst_model': 'AR2220',
                'link_type': 'routed'
            }
        ]
        
        allocated = self.allocator.allocate_interfaces(connections)
        self.assertEqual(len(allocated), 2)
        
        # 验证trunk连接优先分配
        trunk_conn = next(c for c in allocated if c['link_type'] == 'trunk')
        routed_conn = next(c for c in allocated if c['link_type'] == 'routed')
        
        self.assertIsNotNone(trunk_conn['src_interface_name'])
        self.assertIsNotNone(routed_conn['src_interface_name'])

class TestENSPDeployer(unittest.TestCase):
    """测试eNSP部署器"""
    
    def setUp(self):
        self.deployer = ENSPDeployer()
    
    @patch('os.path.exists')
    def test_find_ensp_path(self, mock_exists):
        """测试查找eNSP路径"""
        # 模拟找到eNSP
        mock_exists.side_effect = lambda path: path == "C:\\Program Files\\eNSP\\eNSP.exe"
        
        deployer = ENSPDeployer()
        self.assertEqual(deployer.ensp_path, "C:\\Program Files\\eNSP\\eNSP.exe")
    
    def test_deploy_topology(self):
        """测试部署拓扑"""
        with tempfile.TemporaryDirectory() as temp_dir:
            # 临时修改拓扑路径
            self.deployer.topo_path = temp_dir
            
            test_xml = '<?xml version="1.0"?><topo><devices></devices></topo>'
            result = self.deployer.deploy_topology(test_xml, "test_topo")
            
            self.assertTrue(result)
            topo_file = os.path.join(temp_dir, "test_topo.topo")
            self.assertTrue(os.path.exists(topo_file))
            
            with open(topo_file, 'r') as f:
                content = f.read()
                self.assertEqual(content, test_xml)

class TestNetworkTester(unittest.TestCase):
    """测试网络测试器"""
    
    def setUp(self):
        self.tester = NetworkTester()
    
    @patch('subprocess.run')
    def test_ping_test(self, mock_run):
        """测试ping测试"""
        # 模拟成功的ping
        mock_run.return_value = MagicMock(returncode=0, stdout="Ping successful")
        
        result = self.tester.ping_test("***********", "192.168.1.2")
        
        self.assertTrue(result['success'])
        self.assertEqual(result['source'], "***********")
        self.assertEqual(result['target'], "192.168.1.2")
        self.assertEqual(result['test_type'], 'ping')
    
    def test_generate_test_report(self):
        """测试生成测试报告"""
        # 添加一些测试结果
        self.tester.test_results = [
            {'test_type': 'ping', 'source': '1.1.1.1', 'target': '2.2.2.2', 'success': True},
            {'test_type': 'ping', 'source': '1.1.1.1', 'target': '3.3.3.3', 'success': False}
        ]
        
        report = self.tester.generate_test_report()
        
        self.assertIn('# 网络测试报告', report)
        self.assertIn('总测试数: 2', report)
        self.assertIn('成功测试: 1', report)
        self.assertIn('失败测试: 1', report)

class TestIntegration(unittest.TestCase):
    """集成测试"""
    
    def test_complete_workflow(self):
        """测试完整工作流程"""
        # 创建测试需求
        test_requirement = {
            "topology_name": "integration_test",
            "devices": [
                {"id": "1", "name": "R1", "model": "AR2220"},
                {"id": "2", "name": "SW1", "model": "S5700"}
            ],
            "vlans": [{"id": 10, "description": "Test VLAN"}],
            "routing_protocol": "static",
            "security_features": [],
            "connections": [
                {"src_device": "1", "dst_device": "2", "src_model": "AR2220", "dst_model": "S5700"}
            ],
            "device_configs": {
                "R1": {
                    "device_name": "R1",
                    "vlans": [{"id": 10, "description": "Test VLAN"}],
                    "interfaces": [
                        {
                            "name": "GigabitEthernet0/0/1",
                            "ip": "***********",
                            "mask": "*************"
                        }
                    ],
                    "static_routes": [],
                    "acls": []
                }
            }
        }
        
        # 保存测试需求文件
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            json.dump(test_requirement, f, indent=2)
            requirement_file = f.name
        
        try:
            # 创建编排器
            orchestrator = EnhancedAutomationOrchestrator()
            
            # 测试各个组件
            db = orchestrator.device_db
            self.assertIsNotNone(db.get_device_info("AR2220"))
            
            allocator = orchestrator.interface_allocator
            allocated = allocator.allocate_interfaces(test_requirement['connections'])
            self.assertEqual(len(allocated), 1)
            
            config_gen = orchestrator.config_generator
            config = config_gen.generate_complete_config(
                test_requirement['devices'][0],
                test_requirement['device_configs']['R1']
            )
            self.assertIn('sysname R1', config)
            
        finally:
            # 清理测试文件
            os.unlink(requirement_file)

def run_tests():
    """运行所有测试"""
    # 创建测试套件
    test_suite = unittest.TestSuite()
    
    # 添加测试类
    test_classes = [
        TestHuaweiDeviceDatabase,
        TestTopologyGenerator,
        TestConfigTemplates,
        TestSmartInterfaceAllocator,
        TestENSPDeployer,
        TestNetworkTester,
        TestIntegration
    ]
    
    for test_class in test_classes:
        tests = unittest.TestLoader().loadTestsFromTestCase(test_class)
        test_suite.addTests(tests)
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    # 输出测试结果
    print(f"\n{'='*50}")
    print(f"测试完成!")
    print(f"运行测试: {result.testsRun}")
    print(f"失败: {len(result.failures)}")
    print(f"错误: {len(result.errors)}")
    print(f"成功率: {((result.testsRun - len(result.failures) - len(result.errors)) / result.testsRun * 100):.1f}%")
    
    if result.failures:
        print(f"\n失败的测试:")
        for test, traceback in result.failures:
            print(f"- {test}: {traceback.split('AssertionError:')[-1].strip()}")
    
    if result.errors:
        print(f"\n错误的测试:")
        for test, traceback in result.errors:
            print(f"- {test}: {traceback.split('Error:')[-1].strip()}")
    
    return result.wasSuccessful()

if __name__ == "__main__":
    success = run_tests()
    sys.exit(0 if success else 1)
