#
# Agg-SW1 交换机配置
# 生成时间: 2024-12-19 自动生成
#
sysname Agg-SW1
#
# 系统配置
clock timezone China-Standard-Time add 08:00:00
#
# 用户配置
aaa
 local-user admin password irreversible-cipher <PERSON><PERSON><PERSON>@123
 local-user admin privilege level 15
 local-user admin service-type telnet ssh http
#
# SSH配置
ssh server enable
ssh server-source all-interface
#
# VLAN批量创建
vlan batch 10 20 100
vlan 10
 description Teaching Building VLAN
#
vlan 20
 description Office Building VLAN
#
vlan 100
 description Management VLAN
#
# 接口配置
interface GigabitEthernet0/0/1
 description Teaching Building PC1
 port link-type access
 port default vlan 10
 port-security enable
 port-security max-mac-num 1
 port-security mac-address sticky
 port-security violation protect
 storm-control broadcast min 100 max 1000
 storm-control multicast min 100 max 1000
 undo shutdown
#
interface GigabitEthernet0/0/2
 description Teaching Building PC2
 port link-type access
 port default vlan 10
 port-security enable
 port-security max-mac-num 1
 port-security mac-address sticky
 port-security violation protect
 storm-control broadcast min 100 max 1000
 storm-control multicast min 100 max 1000
 undo shutdown
#
interface GigabitEthernet0/0/3
 description Office Building PC
 port link-type access
 port default vlan 20
 port-security enable
 port-security max-mac-num 1
 port-security mac-address sticky
 port-security violation protect
 undo shutdown
#
interface GigabitEthernet0/0/24
 description Uplink to Core-R1
 port link-type trunk
 port trunk allow-pass vlan 10 20 100
 undo shutdown
#
# 管理VLAN接口
interface Vlanif100
 description Management Interface
 ip address ********** *************
#
# STP配置
stp mode rstp
stp priority 4096
stp enable
#
# 端口STP配置
interface GigabitEthernet0/0/1
 stp edged-port enable
#
interface GigabitEthernet0/0/2
 stp edged-port enable
#
interface GigabitEthernet0/0/3
 stp edged-port enable
#
# DHCP Snooping配置
dhcp snooping enable
dhcp snooping enable vlan 10 20
interface GigabitEthernet0/0/24
 dhcp snooping trusted
#
# QoS配置
traffic classifier teaching_class
 if-match vlan-id 10
#
traffic behavior teaching_behavior
 car cir 10000 pir 20000 cbs 1000000 pbs 2000000 green pass yellow pass red discard
#
traffic policy teaching_policy
 classifier teaching_class behavior teaching_behavior
#
interface GigabitEthernet0/0/1
 traffic-policy teaching_policy inbound
#
interface GigabitEthernet0/0/2
 traffic-policy teaching_policy inbound
#
# 镜像配置
observe-port 1 interface GigabitEthernet0/0/23
interface GigabitEthernet0/0/1
 port-mirroring to observe-port 1 both
#
# 日志配置
info-center enable
info-center loghost ************
#
# NTP配置
ntp-service unicast-server ************
#
# SNMP配置
snmp-agent
snmp-agent local-engineid 800007DB03000000000001
snmp-agent community read public
snmp-agent community write private
#
return
