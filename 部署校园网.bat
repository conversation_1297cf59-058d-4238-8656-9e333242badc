@echo off
chcp 65001 >nul
echo ========================================
echo 校园网拓扑自动部署脚本
echo 基于配置文档要求生成完整校园网
echo ========================================
echo.

:: 检查当前目录文件
echo 🔍 检查拓扑和配置文件...
set "MISSING_FILES="

if not exist "campus_network_complete.topo" (
    echo ❌ 未找到拓扑文件: campus_network_complete.topo
    set "MISSING_FILES=1"
)

if not exist "AR1.cfg" (
    echo ❌ 未找到配置文件: AR1.cfg
    set "MISSING_FILES=1"
)

if not exist "AR2.cfg" (
    echo ❌ 未找到配置文件: AR2.cfg
    set "MISSING_FILES=1"
)

if not exist "SW1.cfg" (
    echo ❌ 未找到配置文件: SW1.cfg
    set "MISSING_FILES=1"
)

if not exist "SW2.cfg" (
    echo ❌ 未找到配置文件: SW2.cfg
    set "MISSING_FILES=1"
)

if not exist "FW.cfg" (
    echo ❌ 未找到配置文件: FW.cfg
    set "MISSING_FILES=1"
)

if defined MISSING_FILES (
    echo.
    echo ❌ 缺少必要文件，请确保所有文件都在当前目录！
    pause
    goto :end
)

echo ✅ 所有必要文件检查完成
echo.

echo 📁 创建eNSP目录结构...

:: 创建拓扑目录
set "TOPO_DIR=%USERPROFILE%\Documents\eNSP\topology"
if not exist "%TOPO_DIR%" (
    mkdir "%TOPO_DIR%"
    echo ✅ 创建拓扑目录: %TOPO_DIR%
) else (
    echo ✅ 拓扑目录已存在: %TOPO_DIR%
)

:: 创建配置目录
set "CONFIG_DIR=%USERPROFILE%\AppData\Local\eNSP\config"
if not exist "%CONFIG_DIR%" (
    mkdir "%CONFIG_DIR%"
    echo ✅ 创建配置目录: %CONFIG_DIR%
) else (
    echo ✅ 配置目录已存在: %CONFIG_DIR%
)

echo.
echo 📋 部署文件到eNSP目录...

:: 复制拓扑文件
copy "campus_network_complete.topo" "%TOPO_DIR%\" >nul
if %errorlevel% equ 0 (
    echo ✅ 校园网拓扑文件部署成功
) else (
    echo ❌ 拓扑文件部署失败
    goto :error
)

:: 复制配置文件
copy "AR1.cfg" "%CONFIG_DIR%\" >nul
copy "AR2.cfg" "%CONFIG_DIR%\" >nul
copy "SW1.cfg" "%CONFIG_DIR%\" >nul
copy "SW2.cfg" "%CONFIG_DIR%\" >nul
copy "FW.cfg" "%CONFIG_DIR%\" >nul

if %errorlevel% equ 0 (
    echo ✅ 所有设备配置文件部署成功
) else (
    echo ❌ 配置文件部署失败
    goto :error
)

echo.
echo 🔍 验证拓扑文件格式...
findstr /C:"<?xml" campus_network_complete.topo >nul
if %errorlevel% equ 0 (
    echo ✅ XML头部格式正确
) else (
    echo ❌ XML头部格式错误
    goto :error
)

findstr /C:"</topo>" campus_network_complete.topo >nul
if %errorlevel% equ 0 (
    echo ✅ XML结构完整
) else (
    echo ❌ XML结构不完整
    goto :error
)

echo.
echo 🎉 校园网拓扑部署完成！
echo.
echo 📋 网络拓扑信息：
echo ┌─────────────────────────────────────────┐
echo │            校园网拓扑结构               │
echo ├─────────────────────────────────────────┤
echo │ 核心设备：                              │
echo │   • AR1 - 核心路由器1 (教学楼/逸夫楼)   │
echo │   • AR2 - 核心路由器2 (自习室/阅览室)   │
echo │   • FW  - 防火墙 (安全网关)             │
echo │                                         │
echo │ 汇聚设备：                              │
echo │   • SW1 - 汇聚交换机1                   │
echo │   • SW2 - 汇聚交换机2                   │
echo │                                         │
echo │ 服务设备：                              │
echo │   • DNS-Server (10.0.1.10)             │
echo │   • FTP-Server (10.0.1.20)             │
echo │   • AC - 无线控制器                     │
echo │   • AP - 无线接入点                     │
echo └─────────────────────────────────────────┘
echo.
echo 📊 VLAN规划：
echo   • VLAN 10: 教学楼 (10.0.10.0/24)
echo   • VLAN 20: 逸夫楼 (10.0.20.0/24)
echo   • VLAN 30: 自习室 (10.0.30.0/24)
echo   • VLAN 40: 阅览室 (10.0.40.0/24)
echo   • VLAN 50: 无线网络 (10.0.50.0/24)
echo   • VLAN 100: 管理网络 (10.0.100.0/24)
echo   • DMZ区: 服务器区 (10.0.1.0/24)
echo.
echo 🔧 下一步操作：
echo 1. 启动eNSP软件
echo 2. 文件 → 打开 → 选择 campus_network_complete.topo
echo 3. 按以下顺序启动设备：
echo    a) 先启动核心路由器 AR1, AR2
echo    b) 再启动防火墙 FW
echo    c) 然后启动交换机 SW1, SW2
echo    d) 最后启动服务器和PC
echo 4. 等待所有设备启动完成（约5-10分钟）
echo 5. 验证网络连通性
echo.
echo 🧪 测试建议：
echo • 教学楼PC ping 10.0.10.1 (网关测试)
echo • 跨VLAN通信测试: 10.0.10.10 ping 10.0.20.10
echo • DNS服务测试: nslookup www.example.com 10.0.1.10
echo • 外网访问测试: ping 8.8.8.8
echo.
echo 📖 如遇问题请查看: 故障排除指南.md
echo.
pause
goto :end

:error
echo.
echo ❌ 部署失败！请检查：
echo 1. 确保所有文件都在当前目录
echo 2. 检查文件权限
echo 3. 确保eNSP已正确安装
echo 4. 检查磁盘空间是否充足
echo.
pause

:end
