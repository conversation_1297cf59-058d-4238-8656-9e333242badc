#
# SW1 汇聚交换机配置
# 接入教学楼、逸夫楼、无线、AR1、AR2
#
sysname SW1
#
# 用户配置
aaa
 local-user admin password irreversible-cipher <PERSON><PERSON><PERSON>@123
 local-user admin privilege level 15
 local-user admin service-type telnet ssh http
#
# SSH配置
ssh server enable
ssh server-source all-interface
#
# VLAN批量创建
vlan batch 10 20 50 100 101
vlan 10
 description Teaching Building VLAN
#
vlan 20
 description Yifu Building VLAN
#
vlan 50
 description Wireless VLAN
#
vlan 100
 description Management VLAN
#
vlan 101
 description Business VLAN
#
# 接入端口配置
interface GigabitEthernet0/0/1
 description Teaching Building PC
 port link-type access
 port default vlan 10
 port-security enable
 port-security max-mac-num 1
 port-security mac-address sticky
 port-security violation protect
 storm-control broadcast min 100 max 1000
 storm-control multicast min 100 max 1000
 undo shutdown
#
interface GigabitEthernet0/0/2
 description Yifu Building PC
 port link-type access
 port default vlan 20
 port-security enable
 port-security max-mac-num 1
 port-security mac-address sticky
 port-security violation protect
 storm-control broadcast min 100 max 1000
 storm-control multicast min 100 max 1000
 undo shutdown
#
interface GigabitEthernet0/0/3
 description Wireless AC
 port link-type access
 port default vlan 50
 undo shutdown
#
# 上联端口配置
interface GigabitEthernet0/0/24
 description Uplink to AR1
 port link-type trunk
 port trunk allow-pass vlan 10 20 50 100 101
 undo shutdown
#
# 管理VLAN接口
interface Vlanif100
 description Management Interface
 ip address *********** *************
#
# STP配置
stp mode rstp
stp priority 4096
stp enable
#
# DHCP Snooping配置
dhcp snooping enable
dhcp snooping enable vlan 10 20 50
interface GigabitEthernet0/0/24
 dhcp snooping trusted
#
# 日志配置
info-center enable
info-center loghost ************
#
# SNMP配置
snmp-agent
snmp-agent community read public
snmp-agent community write private
#
return
