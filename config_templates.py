#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
华为设备配置模板库
包含各种设备类型的详细配置模板
"""

from jinja2 import Template

class HuaweiConfigTemplates:
    """华为设备配置模板类"""
    
    def __init__(self):
        self.templates = {}
        self._load_all_templates()
    
    def _load_all_templates(self):
        """加载所有配置模板"""
        self.templates = {
            'router_basic': self._get_router_basic_template(),
            'router_ospf': self._get_router_ospf_template(),
            'switch_basic': self._get_switch_basic_template(),
            'switch_advanced': self._get_switch_advanced_template(),
            'firewall_basic': self._get_firewall_basic_template(),
            'ac_basic': self._get_ac_basic_template(),
            'ap_basic': self._get_ap_basic_template()
        }
    
    def _get_router_basic_template(self):
        """路由器基础配置模板"""
        return Template("""
#
# {{ device_name }} 路由器配置
# 生成时间: {{ timestamp }}
#
sysname {{ device_name }}
#
# 系统配置
clock timezone {{ timezone | default('China-Standard-Time') }} add 08:00:00
#
# 用户配置
aaa
 local-user admin password irreversible-cipher {{ admin_password | default('Huawei@123') }}
 local-user admin privilege level 15
 local-user admin service-type telnet ssh http
#
# SSH配置
ssh server enable
ssh server-source all-interface
#
# SNMP配置
{% if snmp_enabled %}
snmp-agent
snmp-agent local-engineid {{ snmp_engine_id | default('800007DB03000000000000') }}
snmp-agent community read {{ snmp_community | default('public') }}
{% endif %}
#
# VLAN配置
{% for vlan in vlans %}
vlan {{ vlan.id }}
 description {{ vlan.description }}
#
{% endfor %}
#
# 接口配置
{% for interface in interfaces %}
interface {{ interface.name }}
{% if interface.description %}
 description {{ interface.description }}
{% endif %}
{% if interface.ip and interface.mask %}
 ip address {{ interface.ip }} {{ interface.mask }}
{% endif %}
{% if interface.vlan %}
 port link-type {{ interface.link_type | default('access') }}
{% if interface.link_type == 'trunk' %}
 port trunk allow-pass vlan {{ interface.vlans | join(' ') }}
{% else %}
 port default vlan {{ interface.vlan }}
{% endif %}
{% endif %}
{% if interface.dhcp_enable %}
 dhcp select interface
{% endif %}
 undo shutdown
#
{% endfor %}
#
# DHCP配置
{% if dhcp_pools %}
dhcp enable
{% for pool in dhcp_pools %}
ip pool {{ pool.name }}
 gateway-list {{ pool.gateway }}
 network {{ pool.network }} mask {{ pool.mask }}
 dns-list {{ pool.dns | join(' ') }}
 lease day {{ pool.lease_days | default(1) }}
#
{% endfor %}
{% endif %}
#
# 静态路由配置
{% for route in static_routes %}
ip route-static {{ route.destination }} {{ route.mask }} {{ route.next_hop }}{% if route.preference %} preference {{ route.preference }}{% endif %}
{% endfor %}
#
# ACL配置
{% for acl in acls %}
acl number {{ acl.number }}
{% if acl.description %}
 description {{ acl.description }}
{% endif %}
{% for rule in acl.rules %}
 rule {{ rule.id | default('') }} {{ rule.action }} {{ rule.protocol }}{% if rule.source %} source {{ rule.source }}{% endif %}{% if rule.destination %} destination {{ rule.destination }}{% endif %}{% if rule.port %} destination-port eq {{ rule.port }}{% endif %}
{% endfor %}
#
{% endfor %}
#
# NAT配置
{% if nat_rules %}
{% for nat in nat_rules %}
{% if nat.type == 'static' %}
nat static global {{ nat.global_ip }} inside {{ nat.inside_ip }}
{% elif nat.type == 'outbound' %}
acl number {{ nat.acl_number }}
 rule permit ip source {{ nat.source_network }}
interface {{ nat.interface }}
 nat outbound {{ nat.acl_number }}
{% endif %}
{% endfor %}
{% endif %}
#
# 保存配置
return
""")
    
    def _get_router_ospf_template(self):
        """路由器OSPF配置模板"""
        return Template("""
#
# OSPF路由协议配置
#
ospf {{ process_id | default(1) }}
 router-id {{ router_id }}
{% if default_route_advertise %}
 default-route-advertise
{% endif %}
{% for area in areas %}
 area {{ area.id }}
{% for network in area.networks %}
  network {{ network.address }} {{ network.wildcard }}
{% endfor %}
{% if area.stub %}
  stub
{% endif %}
{% if area.nssa %}
  nssa
{% endif %}
{% endfor %}
#
# OSPF接口配置
{% for interface in ospf_interfaces %}
interface {{ interface.name }}
{% if interface.cost %}
 ospf cost {{ interface.cost }}
{% endif %}
{% if interface.priority %}
 ospf dr-priority {{ interface.priority }}
{% endif %}
{% if interface.hello_interval %}
 ospf timer hello {{ interface.hello_interval }}
{% endif %}
{% if interface.dead_interval %}
 ospf timer dead {{ interface.dead_interval }}
{% endif %}
{% if interface.authentication %}
 ospf authentication-mode {{ interface.authentication.mode }}{% if interface.authentication.key %} {{ interface.authentication.key }}{% endif %}
{% endif %}
#
{% endfor %}
""")
    
    def _get_switch_basic_template(self):
        """交换机基础配置模板"""
        return Template("""
#
# {{ device_name }} 交换机配置
# 生成时间: {{ timestamp }}
#
sysname {{ device_name }}
#
# 系统配置
clock timezone {{ timezone | default('China-Standard-Time') }} add 08:00:00
#
# 用户配置
aaa
 local-user admin password irreversible-cipher {{ admin_password | default('Huawei@123') }}
 local-user admin privilege level 15
 local-user admin service-type telnet ssh http
#
# SSH配置
ssh server enable
ssh server-source all-interface
#
# VLAN批量创建
vlan batch {{ vlans | map(attribute='id') | join(' ') }}
{% for vlan in vlans %}
vlan {{ vlan.id }}
 description {{ vlan.description }}
#
{% endfor %}
#
# 接口配置
{% for interface in interfaces %}
interface {{ interface.name }}
{% if interface.description %}
 description {{ interface.description }}
{% endif %}
 port link-type {{ interface.link_type | default('access') }}
{% if interface.link_type == 'trunk' %}
 port trunk allow-pass vlan {{ interface.vlans | join(' ') }}
{% if interface.native_vlan %}
 port trunk pvid vlan {{ interface.native_vlan }}
{% endif %}
{% else %}
 port default vlan {{ interface.vlan }}
{% endif %}
{% if interface.port_security %}
 port-security enable
 port-security max-mac-num {{ interface.max_mac | default(1) }}
 port-security mac-address sticky
{% if interface.violation_action %}
 port-security violation {{ interface.violation_action }}
{% endif %}
{% endif %}
{% if interface.storm_control %}
 storm-control broadcast min {{ interface.storm_control.broadcast_min | default(100) }} max {{ interface.storm_control.broadcast_max | default(1000) }}
 storm-control multicast min {{ interface.storm_control.multicast_min | default(100) }} max {{ interface.storm_control.multicast_max | default(1000) }}
{% endif %}
{% if interface.qos %}
 qos trust {{ interface.qos.trust | default('dscp') }}
{% endif %}
 undo shutdown
#
{% endfor %}
#
# STP配置
{% if stp_enabled %}
stp mode {{ stp_mode | default('rstp') }}
{% if stp_priority %}
stp priority {{ stp_priority }}
{% endif %}
{% for interface in stp_interfaces %}
interface {{ interface.name }}
{% if interface.edge_port %}
 stp edged-port enable
{% endif %}
{% if interface.cost %}
 stp cost {{ interface.cost }}
{% endif %}
{% if interface.priority %}
 stp port priority {{ interface.priority }}
{% endif %}
#
{% endfor %}
{% endif %}
#
# 链路聚合配置
{% for lag in link_aggregation %}
interface Eth-Trunk{{ lag.id }}
 description {{ lag.description }}
 port link-type {{ lag.link_type | default('trunk') }}
{% if lag.link_type == 'trunk' %}
 port trunk allow-pass vlan {{ lag.vlans | join(' ') }}
{% else %}
 port default vlan {{ lag.vlan }}
{% endif %}
 mode {{ lag.mode | default('lacp-static') }}
{% for member in lag.members %}
interface {{ member }}
 eth-trunk {{ lag.id }}
#
{% endfor %}
{% endfor %}
#
# DHCP Snooping配置
{% if dhcp_snooping_enabled %}
dhcp snooping enable
{% for vlan in dhcp_snooping_vlans %}
dhcp snooping enable vlan {{ vlan }}
{% endfor %}
{% for interface in dhcp_trusted_interfaces %}
interface {{ interface }}
 dhcp snooping trusted
#
{% endfor %}
{% endif %}
#
return
""")
    
    def _get_switch_advanced_template(self):
        """交换机高级配置模板"""
        return Template("""
#
# 高级交换机功能配置
#
# VRRP配置
{% for vrrp in vrrp_groups %}
interface {{ vrrp.interface }}
 vrrp vrid {{ vrrp.group_id }} virtual-ip {{ vrrp.virtual_ip }}
 vrrp vrid {{ vrrp.group_id }} priority {{ vrrp.priority | default(100) }}
 vrrp vrid {{ vrrp.group_id }} preempt-mode timer delay {{ vrrp.preempt_delay | default(0) }}
{% if vrrp.authentication %}
 vrrp vrid {{ vrrp.group_id }} authentication-mode {{ vrrp.authentication.mode }} {{ vrrp.authentication.key }}
{% endif %}
#
{% endfor %}
#
# QoS配置
{% if qos_policies %}
{% for policy in qos_policies %}
traffic classifier {{ policy.classifier_name }}
{% for rule in policy.rules %}
 if-match {{ rule.match_type }} {{ rule.value }}
{% endfor %}
#
traffic behavior {{ policy.behavior_name }}
{% for action in policy.actions %}
 {{ action.type }} {{ action.value }}
{% endfor %}
#
traffic policy {{ policy.policy_name }}
 classifier {{ policy.classifier_name }} behavior {{ policy.behavior_name }}
#
{% endfor %}
{% endif %}
#
# 镜像配置
{% if mirror_sessions %}
{% for session in mirror_sessions %}
observe-port {{ session.id }} interface {{ session.destination }}
{% for source in session.sources %}
interface {{ source.interface }}
 port-mirroring to observe-port {{ session.id }} {{ source.direction | default('both') }}
#
{% endfor %}
{% endfor %}
{% endif %}
#
return
""")
    
    def _get_firewall_basic_template(self):
        """防火墙基础配置模板"""
        return Template("""
#
# {{ device_name }} 防火墙配置
# 生成时间: {{ timestamp }}
#
sysname {{ device_name }}
#
# 安全区域配置
{% for zone in security_zones %}
security-zone name {{ zone.name }}
 description {{ zone.description }}
 priority {{ zone.priority | default(50) }}
{% for interface in zone.interfaces %}
 import interface {{ interface }}
{% endfor %}
#
{% endfor %}
#
# 安全策略配置
security-policy
{% for policy in security_policies %}
 rule name {{ policy.name }}
  source-zone {{ policy.source_zone }}
  destination-zone {{ policy.destination_zone }}
{% if policy.source_address %}
  source-address {{ policy.source_address }}
{% endif %}
{% if policy.destination_address %}
  destination-address {{ policy.destination_address }}
{% endif %}
{% if policy.service %}
  service {{ policy.service }}
{% endif %}
  action {{ policy.action }}
{% if policy.logging %}
  logging
{% endif %}
{% endfor %}
#
# NAT策略配置
{% for nat_policy in nat_policies %}
nat-policy
 rule name {{ nat_policy.name }}
  source-zone {{ nat_policy.source_zone }}
  destination-zone {{ nat_policy.destination_zone }}
{% if nat_policy.source_address %}
  source-address {{ nat_policy.source_address }}
{% endif %}
{% if nat_policy.destination_address %}
  destination-address {{ nat_policy.destination_address }}
{% endif %}
  action {{ nat_policy.action }}
{% if nat_policy.translated_address %}
  translated-address {{ nat_policy.translated_address }}
{% endif %}
{% endfor %}
#
return
""")
    
    def _get_ac_basic_template(self):
        """无线控制器基础配置模板"""
        return Template("""
#
# {{ device_name }} 无线控制器配置
# 生成时间: {{ timestamp }}
#
sysname {{ device_name }}
#
# WLAN配置
{% for wlan in wlans %}
wlan
 ssid {{ wlan.ssid }}
{% if wlan.security %}
 security-profile {{ wlan.security.profile_name }}
{% endif %}
 service-template {{ wlan.service_template | default('default') }}
#
{% endfor %}
#
# 安全配置文件
{% for security in security_profiles %}
security-profile name {{ security.name }}
{% if security.type == 'wpa2-psk' %}
 security wpa2 psk pass-phrase {{ security.passphrase }} aes
{% elif security.type == 'wpa2-enterprise' %}
 security wpa2 dot1x aes
{% endif %}
#
{% endfor %}
#
# AP组配置
{% for ap_group in ap_groups %}
ap-group name {{ ap_group.name }}
{% for ap in ap_group.aps %}
 ap-id {{ ap.id }} ap-mac {{ ap.mac }}
 ap-name {{ ap.name }}
 ap-group {{ ap_group.name }}
{% endfor %}
#
{% endfor %}
#
# CAPWAP配置
capwap
 source interface {{ capwap_source_interface | default('Vlanif1') }}
#
return
""")
    
    def _get_ap_basic_template(self):
        """无线AP基础配置模板"""
        return Template("""
#
# {{ device_name }} 无线AP配置
# 生成时间: {{ timestamp }}
#
sysname {{ device_name }}
#
# 基础配置
{% for wlan_bss in wlan_bss_interfaces %}
interface {{ wlan_bss.name }}
 ssid {{ wlan_bss.ssid }}
{% if wlan_bss.security %}
 security-profile {{ wlan_bss.security }}
{% endif %}
 service-template {{ wlan_bss.service_template | default('default') }}
#
{% endfor %}
#
# 有线接口配置
{% for interface in wired_interfaces %}
interface {{ interface.name }}
{% if interface.description %}
 description {{ interface.description }}
{% endif %}
{% if interface.ip and interface.mask %}
 ip address {{ interface.ip }} {{ interface.mask }}
{% endif %}
{% if interface.vlan %}
 port link-type access
 port default vlan {{ interface.vlan }}
{% endif %}
 undo shutdown
#
{% endfor %}
#
# CAPWAP配置
capwap client
 server ip {{ ac_ip }}
 source interface {{ capwap_source_interface | default('GigabitEthernet0/0/1') }}
#
return
""")
    
    def get_template(self, template_name: str) -> Template:
        """获取指定的配置模板"""
        return self.templates.get(template_name)
    
    def get_available_templates(self) -> list:
        """获取所有可用的模板名称"""
        return list(self.templates.keys())
