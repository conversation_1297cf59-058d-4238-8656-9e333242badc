#
# Core-R1 路由器配置
# 生成时间: 2024-12-19 自动生成
#
sysname Core-R1
#
# 系统配置
clock timezone China-Standard-Time add 08:00:00
#
# 用户配置
aaa
 local-user admin password irreversible-cipher <PERSON><PERSON><PERSON>@123
 local-user admin privilege level 15
 local-user admin service-type telnet ssh http
#
# SSH配置
ssh server enable
ssh server-source all-interface
#
# SNMP配置
snmp-agent
snmp-agent local-engineid 800007DB03000000000000
snmp-agent community read public
#
# VLAN配置
vlan 10
 description Teaching Building VLAN
#
vlan 20
 description Office Building VLAN
#
vlan 100
 description Management VLAN
#
# 接口配置
interface GigabitEthernet0/0/0
 description To Agg-SW1 - Trunk
 port link-type trunk
 port trunk allow-pass vlan 10 20 100
 undo shutdown
#
interface GigabitEthernet0/0/1
 description To Firewall - Routed
 ip address ********** ***************
 undo shutdown
#
interface Vlanif10
 description Teaching Building Gateway
 ip address ********* *************
 dhcp select interface
#
interface Vlanif20
 description Office Building Gateway
 ip address ********* *************
 dhcp select interface
#
interface Vlanif100
 description Management Gateway
 ip address ********** *************
#
# DHCP配置
dhcp enable
ip pool teaching_pool
 gateway-list *********
 network ********* mask *************
 dns-list ******* ***************
 lease day 1
#
ip pool office_pool
 gateway-list *********
 network ********* mask *************
 dns-list ******* ***************
 lease day 1
#
# OSPF配置
ospf 1
 router-id *******
 area 0.0.0.0
  network ********* *********
  network ********* *********
  network ********** *******
#
# 静态路由
ip route-static 0.0.0.0 0.0.0.0 **********
#
# ACL配置
acl number 3000
 description Block Teaching to Management
 rule 5 deny ip source ********* ********* destination ********** *********
 rule 10 permit ip
#
# 应用ACL到接口
interface Vlanif10
 packet-filter 3000 outbound
#
# 日志配置
info-center enable
info-center loghost **********00
#
# NTP配置
ntp-service unicast-server ************
#
return
