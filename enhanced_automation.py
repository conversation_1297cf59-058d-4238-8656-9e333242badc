#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强版eNSP网络自动化工具
集成配置模板、智能接口分配、设备信息查询等功能
"""

import json
import os
import time
import logging
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any, Optional
import requests
from dataclasses import dataclass

from ensp_automation import (
    HuaweiDeviceDatabase, TopologyGenerator, ENSPDeployer, 
    NetworkTester, AutomationOrchestrator, NetworkRequirement
)
from config_templates import HuaweiConfigTemplates

logger = logging.getLogger(__name__)

class EnhancedConfigGenerator:
    """增强版配置生成器"""
    
    def __init__(self, device_db: HuaweiDeviceDatabase):
        self.device_db = device_db
        self.templates = HuaweiConfigTemplates()
    
    def generate_complete_config(self, device: Dict, network_config: Dict) -> str:
        """生成完整的设备配置"""
        device_info = self.device_db.get_device_info(device['model'])
        
        if not device_info:
            logger.error(f"Unknown device model: {device['model']}")
            return ""
        
        # 添加时间戳
        network_config['timestamp'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        
        # 根据设备类型选择合适的模板
        if device_info.type == 'router':
            if 'ospf' in network_config.get('routing_protocol', ''):
                template = self.templates.get_template('router_ospf')
                config = template.render(**network_config)
                # 合并基础配置
                basic_template = self.templates.get_template('router_basic')
                basic_config = basic_template.render(**network_config)
                return basic_config + "\n" + config
            else:
                template = self.templates.get_template('router_basic')
                return template.render(**network_config)
                
        elif device_info.type == 'switch':
            if network_config.get('advanced_features'):
                template = self.templates.get_template('switch_advanced')
                config = template.render(**network_config)
                # 合并基础配置
                basic_template = self.templates.get_template('switch_basic')
                basic_config = basic_template.render(**network_config)
                return basic_config + "\n" + config
            else:
                template = self.templates.get_template('switch_basic')
                return template.render(**network_config)
                
        elif device_info.type == 'firewall':
            template = self.templates.get_template('firewall_basic')
            return template.render(**network_config)
            
        elif device_info.type == 'ac':
            template = self.templates.get_template('ac_basic')
            return template.render(**network_config)
            
        elif device_info.type == 'ap':
            template = self.templates.get_template('ap_basic')
            return template.render(**network_config)
        
        else:
            logger.warning(f"No template available for device type: {device_info.type}")
            return ""

class SmartInterfaceAllocator:
    """智能接口分配器"""
    
    def __init__(self, device_db: HuaweiDeviceDatabase):
        self.device_db = device_db
        self.interface_usage = {}
    
    def allocate_interfaces(self, connections: List[Dict]) -> List[Dict]:
        """智能分配接口连接"""
        allocated_connections = []
        
        # 按连接类型排序，优先分配高速接口给trunk连接
        sorted_connections = sorted(connections, key=lambda x: self._get_priority(x))
        
        for conn in sorted_connections:
            src_device = conn['src_device']
            dst_device = conn['dst_device']
            link_type = conn.get('link_type', 'access')
            
            # 获取设备信息
            src_model = conn.get('src_model', 'AR2220')
            dst_model = conn.get('dst_model', 'AR2220')
            src_info = self.device_db.get_device_info(src_model)
            dst_info = self.device_db.get_device_info(dst_model)
            
            # 分配源设备接口
            src_interface = self._allocate_best_interface(src_device, src_info, link_type)
            dst_interface = self._allocate_best_interface(dst_device, dst_info, link_type)
            
            allocated_connections.append({
                **conn,
                'src_interface': src_interface['id'],
                'src_interface_name': src_interface['name'],
                'dst_interface': dst_interface['id'],
                'dst_interface_name': dst_interface['name']
            })
        
        return allocated_connections
    
    def _get_priority(self, connection: Dict) -> int:
        """获取连接优先级，数值越小优先级越高"""
        link_type = connection.get('link_type', 'access')
        if link_type == 'trunk':
            return 1  # trunk连接优先级最高
        elif link_type == 'routed':
            return 2  # 路由连接次之
        else:
            return 3  # access连接最低
    
    def _allocate_best_interface(self, device_id: str, device_info, link_type: str) -> Dict:
        """为设备分配最佳接口"""
        if device_id not in self.interface_usage:
            self.interface_usage[device_id] = {}
        
        # 根据连接类型选择合适的接口类型
        preferred_types = self._get_preferred_interface_types(device_info, link_type)
        
        for interface_type in preferred_types:
            for interface_spec in device_info.interfaces:
                if interface_spec['type'] == interface_type:
                    # 查找可用接口
                    for i in range(interface_spec['count']):
                        interface_name = f"{interface_spec['prefix']}{i}"
                        if interface_name not in self.interface_usage[device_id]:
                            # 分配接口
                            interface_id = len(self.interface_usage[device_id])
                            self.interface_usage[device_id][interface_name] = {
                                'id': interface_id,
                                'type': interface_type,
                                'link_type': link_type
                            }
                            return {
                                'id': interface_id,
                                'name': interface_name,
                                'type': interface_type
                            }
        
        # 如果没有找到合适的接口，使用第一个可用接口
        return self._allocate_any_available_interface(device_id, device_info)
    
    def _get_preferred_interface_types(self, device_info, link_type: str) -> List[str]:
        """根据连接类型获取首选接口类型"""
        if link_type == 'trunk':
            # trunk连接优先使用高速接口
            return ['10GE', 'GigabitEthernet', 'FastEthernet']
        elif link_type == 'routed':
            # 路由连接优先使用千兆接口
            return ['GigabitEthernet', '10GE', 'FastEthernet']
        else:
            # access连接可以使用任何接口
            return ['FastEthernet', 'GigabitEthernet', '10GE']
    
    def _allocate_any_available_interface(self, device_id: str, device_info) -> Dict:
        """分配任何可用接口"""
        for interface_spec in device_info.interfaces:
            for i in range(interface_spec['count']):
                interface_name = f"{interface_spec['prefix']}{i}"
                if interface_name not in self.interface_usage[device_id]:
                    interface_id = len(self.interface_usage[device_id])
                    self.interface_usage[device_id][interface_name] = {
                        'id': interface_id,
                        'type': interface_spec['type']
                    }
                    return {
                        'id': interface_id,
                        'name': interface_name,
                        'type': interface_spec['type']
                    }
        
        # 如果所有接口都被占用，返回默认接口
        return {'id': 0, 'name': 'GigabitEthernet0/0/0', 'type': 'GigabitEthernet'}

class HuaweiDeviceInfoFetcher:
    """华为设备信息获取器 - 从官网获取最新设备规格"""
    
    def __init__(self):
        self.base_url = "https://support.huawei.com"
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })
    
    def fetch_device_specs(self, model: str) -> Optional[Dict]:
        """从华为官网获取设备规格信息"""
        try:
            # 构建搜索URL
            search_url = f"{self.base_url}/enterprise/zh/search"
            params = {
                'keyword': model,
                'category': 'product'
            }
            
            response = self.session.get(search_url, params=params, timeout=10)
            
            if response.status_code == 200:
                # 解析响应获取设备信息
                device_info = self._parse_device_info(response.text, model)
                return device_info
            else:
                logger.warning(f"Failed to fetch device info for {model}: HTTP {response.status_code}")
                return None
                
        except Exception as e:
            logger.error(f"Error fetching device info for {model}: {str(e)}")
            return None
    
    def _parse_device_info(self, html_content: str, model: str) -> Dict:
        """解析HTML内容获取设备信息"""
        # 这里应该实现HTML解析逻辑
        # 由于华为官网的复杂性，这里返回模拟数据
        return {
            'model': model,
            'interfaces': self._get_default_interfaces(model),
            'specifications': {
                'power_consumption': 'Unknown',
                'dimensions': 'Unknown',
                'weight': 'Unknown'
            }
        }
    
    def _get_default_interfaces(self, model: str) -> List[Dict]:
        """获取默认接口配置"""
        # 基于型号返回默认接口配置
        interface_configs = {
            'AR2220': [
                {'type': 'GigabitEthernet', 'count': 4, 'prefix': 'GigabitEthernet0/0/'},
                {'type': 'Serial', 'count': 2, 'prefix': 'Serial0/0/'}
            ],
            'S5700': [
                {'type': 'GigabitEthernet', 'count': 24, 'prefix': 'GigabitEthernet0/0/'},
                {'type': '10GE', 'count': 4, 'prefix': '10GE1/0/'}
            ]
        }
        
        return interface_configs.get(model, [])

class EnhancedAutomationOrchestrator:
    """增强版自动化编排器"""
    
    def __init__(self):
        self.device_db = HuaweiDeviceDatabase()
        self.topo_generator = TopologyGenerator(self.device_db)
        self.config_generator = EnhancedConfigGenerator(self.device_db)
        self.interface_allocator = SmartInterfaceAllocator(self.device_db)
        self.deployer = ENSPDeployer()
        self.tester = NetworkTester()
        self.device_fetcher = HuaweiDeviceInfoFetcher()
    
    def create_network_from_requirement(self, requirement_file: str) -> bool:
        """从需求文件创建完整网络"""
        try:
            logger.info(f"Loading requirement from: {requirement_file}")
            
            # 1. 加载需求
            with open(requirement_file, 'r', encoding='utf-8') as f:
                requirement_data = json.load(f)
            
            # 2. 验证和更新设备信息
            self._update_device_database(requirement_data['devices'])
            
            # 3. 智能分配接口
            logger.info("Allocating interfaces...")
            allocated_connections = self.interface_allocator.allocate_interfaces(
                requirement_data['connections']
            )
            requirement_data['connections'] = allocated_connections
            
            # 4. 生成拓扑
            logger.info("Generating topology...")
            requirement = NetworkRequirement(**requirement_data)
            topo_xml = self.topo_generator.generate_topology(requirement)
            
            # 5. 生成配置
            logger.info("Generating device configurations...")
            device_configs = {}
            
            for device in requirement.devices:
                device_config_data = requirement_data.get('device_configs', {}).get(device['name'], {})
                if device_config_data:
                    config = self.config_generator.generate_complete_config(device, device_config_data)
                    device_configs[device['name']] = config
                else:
                    logger.warning(f"No configuration data found for device: {device['name']}")
            
            # 6. 部署到eNSP
            logger.info("Deploying to eNSP...")
            self.deployer.deploy_topology(topo_xml, requirement.topology_name)
            self.deployer.deploy_configs(device_configs)
            
            # 7. 启动eNSP
            topo_file = os.path.join(self.deployer.topo_path, f"{requirement.topology_name}.topo")
            self.deployer.start_ensp(topo_file)
            
            # 8. 保存接口分配信息
            self._save_interface_allocation(requirement.topology_name, allocated_connections)
            
            logger.info("Network creation completed successfully!")
            return True
            
        except Exception as e:
            logger.error(f"Failed to create network: {str(e)}")
            return False
    
    def _update_device_database(self, devices: List[Dict]):
        """更新设备数据库"""
        for device in devices:
            model = device['model']
            if not self.device_db.get_device_info(model):
                logger.info(f"Fetching device info for unknown model: {model}")
                device_info = self.device_fetcher.fetch_device_specs(model)
                if device_info:
                    # 更新设备数据库
                    logger.info(f"Updated device database with {model} specifications")
    
    def _save_interface_allocation(self, topology_name: str, connections: List[Dict]):
        """保存接口分配信息"""
        allocation_file = f"{topology_name}_interface_allocation.json"
        with open(allocation_file, 'w', encoding='utf-8') as f:
            json.dump({
                'topology_name': topology_name,
                'timestamp': datetime.now().isoformat(),
                'connections': connections,
                'interface_usage': self.interface_allocator.interface_usage
            }, f, indent=2, ensure_ascii=False)
        
        logger.info(f"Interface allocation saved to: {allocation_file}")
    
    def run_comprehensive_tests(self, test_config: Dict) -> str:
        """运行综合测试"""
        logger.info("Starting comprehensive network tests...")
        
        # 等待设备启动
        startup_time = test_config.get('startup_wait', 90)
        logger.info(f"Waiting {startup_time} seconds for devices to start...")
        time.sleep(startup_time)
        
        # 执行基础连通性测试
        ip_list = test_config.get('test_ips', [])
        if ip_list:
            self.tester.connectivity_matrix_test(ip_list)
        
        # 执行特定连通性测试
        connectivity_tests = test_config.get('connectivity_tests', [])
        for test in connectivity_tests:
            logger.info(f"Running test: {test['name']}")
            for target in test['targets']:
                self.tester.ping_test(test['source'], target)
        
        # 生成测试报告
        report = self.tester.generate_test_report()
        
        # 保存报告
        timestamp = int(time.time())
        report_file = f"comprehensive_test_report_{timestamp}.md"
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(report)
        
        logger.info(f"Comprehensive test report saved to: {report_file}")
        return report_file

def main():
    """主函数"""
    # 配置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('enhanced_automation.log', encoding='utf-8'),
            logging.StreamHandler()
        ]
    )
    
    # 创建增强版编排器
    orchestrator = EnhancedAutomationOrchestrator()
    
    # 使用详细的校园网需求文件
    requirement_file = 'campus_network_requirement.json'
    
    if not os.path.exists(requirement_file):
        logger.error(f"Requirement file not found: {requirement_file}")
        return
    
    # 创建网络
    success = orchestrator.create_network_from_requirement(requirement_file)
    
    if success:
        # 加载测试配置
        with open(requirement_file, 'r', encoding='utf-8') as f:
            requirement_data = json.load(f)
        
        test_config = requirement_data.get('test_config', {})
        orchestrator.run_comprehensive_tests(test_config)
    else:
        logger.error("Network creation failed!")

if __name__ == "__main__":
    main()
