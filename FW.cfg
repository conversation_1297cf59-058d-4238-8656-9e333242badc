#
# FW 防火墙配置
# 连接AR1、AR2、DMZ、ISP
#
sysname FW
#
# 用户配置
aaa
 local-user admin password irreversible-cipher <PERSON><PERSON><PERSON>@123
 local-user admin privilege level 15
 local-user admin service-type telnet ssh http
#
# SSH配置
ssh server enable
ssh server-source all-interface
#
# 接口配置
interface GigabitEthernet1/0/0
 description 连接AR1-内网
 ip address ********** ***************
 undo shutdown
#
interface GigabitEthernet1/0/1
 description 连接AR2-内网
 ip address ********** ***************
 undo shutdown
#
interface GigabitEthernet1/0/2
 description 连接DMZ区
 ip address ******** *************
 undo shutdown
#
interface GigabitEthernet1/0/3
 description 连接ISP-外网
 ip address *********** ***************
 undo shutdown
#
# OSPF配置
ospf 1
 router-id *******
 area 0.0.0.0
  network ********** *******
  network ********** *******
  network ******** *********
  network ********** *******
#
# 安全区域配置
security-zone name trust
 description 内网信任区域
 priority 85
 import interface GigabitEthernet1/0/0
 import interface GigabitEthernet1/0/1
#
security-zone name dmz
 description DMZ区域
 priority 50
 import interface GigabitEthernet1/0/2
#
security-zone name untrust
 description 外网不信任区域
 priority 5
 import interface GigabitEthernet1/0/3
#
# 安全策略配置
security-policy
 rule name allow_internal_to_internet
  source-zone trust
  destination-zone untrust
  source-address any
  destination-address any
  service any
  action permit
  logging
#
 rule name allow_internal_to_dmz
  source-zone trust
  destination-zone dmz
  source-address any
  destination-address any
  service http
  service https
  service ftp
  service dns
  action permit
  logging
#
 rule name allow_dmz_to_internet
  source-zone dmz
  destination-zone untrust
  source-address any
  destination-address any
  service any
  action permit
  logging
#
 rule name deny_internet_to_internal
  source-zone untrust
  destination-zone trust
  source-address any
  destination-address any
  service any
  action deny
  logging
#
 rule name allow_internet_to_dmz_services
  source-zone untrust
  destination-zone dmz
  source-address any
  destination-address ********0
  service dns
  action permit
  logging
#
 rule name allow_internet_to_dmz_ftp
  source-zone untrust
  destination-zone dmz
  source-address any
  destination-address *********
  service ftp
  action permit
  logging
#
# NAT策略配置
nat-policy
 rule name internal_to_internet_snat
  source-zone trust
  destination-zone untrust
  source-address 10.0.0.0 mask *********
  action source-nat
  translated-address *********
#
 rule name dmz_dns_dnat
  source-zone untrust
  destination-zone dmz
  destination-address *********0
  service dns
  action destination-nat
  translated-address ********0
#
 rule name dmz_ftp_dnat
  source-zone untrust
  destination-zone dmz
  destination-address **********
  service ftp
  action destination-nat
  translated-address *********
#
# 静态NAT配置
nat static global *********0 inside ********0
nat static global ********** inside *********
#
# ACL配置
acl number 3000
 description Block Teaching to DMZ
 rule 5 deny ip source ********* ********* destination ******** *********
 rule 10 permit ip
#
acl number 3001
 description Block Study to DMZ
 rule 5 deny ip source ********* ********* destination ******** *********
 rule 10 permit ip
#
acl number 3002
 description Allow Wireless Internet Only
 rule 5 permit ip source ********* ********* destination any
 rule 10 deny ip source ********* *********
#
# 应用ACL
interface GigabitEthernet1/0/0
 packet-filter 3000 inbound
#
interface GigabitEthernet1/0/1
 packet-filter 3001 inbound
#
interface GigabitEthernet1/0/3
 packet-filter 3002 outbound
#
# 攻击防护配置
anti-attack
 tcp-flood enable
 udp-flood enable
 icmp-flood enable
 syn-flood enable
#
# 日志配置
info-center enable
info-center loghost ************
info-center source default channel loghost log level warning
#
# NTP配置
ntp-service unicast-server ************
#
# SNMP配置
snmp-agent
snmp-agent local-engineid 800007DB03000000000003
snmp-agent community read public
snmp-agent community write private
snmp-agent sys-info contact "Security Admin"
snmp-agent sys-info location "Core Room Firewall"
#
# 管理访问控制
acl number 3010
 description Management Access Control
 rule 5 permit ip source ********** *********
 rule 10 deny ip
#
user-interface vty 0 4
 acl 3010 inbound
 authentication-mode aaa
 user privilege level 15
 idle-timeout 30 0
#
return
