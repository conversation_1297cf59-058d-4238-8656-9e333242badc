#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
eNSP 网络拓扑自动化生成与配置部署工具
支持根据需求自动生成拓扑文件、设备配置文件，并部署到eNSP环境
"""

import json
import xml.etree.ElementTree as ET
import os
import shutil
import subprocess
import time
import logging
from pathlib import Path
from typing import Dict, List, Any
from jinja2 import Template
import requests
from dataclasses import dataclass

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('ensp_automation.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

@dataclass
class DeviceInfo:
    """设备信息数据类"""
    model: str
    type: str  # router, switch, firewall, ap, ac, pc, server
    interfaces: List[Dict]
    max_interfaces: int
    supported_features: List[str]

@dataclass
class NetworkRequirement:
    """网络需求数据类"""
    topology_name: str
    devices: List[Dict]
    vlans: List[Dict]
    routing_protocol: str  # static, ospf, bgp
    security_features: List[str]  # acl, nat, firewall
    connections: List[Dict]

class HuaweiDeviceDatabase:
    """华为设备数据库 - 从官网获取设备规格信息"""
    
    def __init__(self):
        self.device_specs = {
            # 路由器系列
            "AR2220": DeviceInfo(
                model="AR2220",
                type="router",
                interfaces=[
                    {"type": "GigabitEthernet", "count": 4, "prefix": "GigabitEthernet0/0/"},
                    {"type": "Serial", "count": 2, "prefix": "Serial0/0/"},
                    {"type": "Console", "count": 1, "prefix": "Console"}
                ],
                max_interfaces=6,
                supported_features=["ospf", "bgp", "nat", "acl", "dhcp", "vlan"]
            ),
            "AR1220": DeviceInfo(
                model="AR1220",
                type="router", 
                interfaces=[
                    {"type": "GigabitEthernet", "count": 2, "prefix": "GigabitEthernet0/0/"},
                    {"type": "FastEthernet", "count": 8, "prefix": "FastEthernet0/0/"}
                ],
                max_interfaces=10,
                supported_features=["ospf", "nat", "acl", "dhcp", "vlan"]
            ),
            # 交换机系列
            "S5700": DeviceInfo(
                model="S5700",
                type="switch",
                interfaces=[
                    {"type": "GigabitEthernet", "count": 24, "prefix": "GigabitEthernet0/0/"},
                    {"type": "10GE", "count": 4, "prefix": "10GE1/0/"}
                ],
                max_interfaces=28,
                supported_features=["vlan", "stp", "lacp", "port-security", "dhcp-snooping"]
            ),
            "S3700": DeviceInfo(
                model="S3700",
                type="switch",
                interfaces=[
                    {"type": "FastEthernet", "count": 24, "prefix": "FastEthernet0/0/"},
                    {"type": "GigabitEthernet", "count": 4, "prefix": "GigabitEthernet0/0/"}
                ],
                max_interfaces=28,
                supported_features=["vlan", "stp", "port-security"]
            ),
            # 防火墙
            "USG6000V": DeviceInfo(
                model="USG6000V",
                type="firewall",
                interfaces=[
                    {"type": "GigabitEthernet", "count": 8, "prefix": "GigabitEthernet1/0/"}
                ],
                max_interfaces=8,
                supported_features=["firewall", "nat", "vpn", "acl", "ids"]
            ),
            # 无线设备
            "AC6005": DeviceInfo(
                model="AC6005",
                type="ac",
                interfaces=[
                    {"type": "GigabitEthernet", "count": 2, "prefix": "GigabitEthernet0/0/"}
                ],
                max_interfaces=2,
                supported_features=["wlan", "capwap", "vlan"]
            ),
            "AP2030DN": DeviceInfo(
                model="AP2030DN",
                type="ap",
                interfaces=[
                    {"type": "GigabitEthernet", "count": 1, "prefix": "GigabitEthernet0/0/"},
                    {"type": "WLAN-BSS", "count": 2, "prefix": "WLAN-BSS"}
                ],
                max_interfaces=3,
                supported_features=["wlan", "capwap"]
            )
        }
    
    def get_device_info(self, model: str) -> DeviceInfo:
        """获取设备信息"""
        return self.device_specs.get(model)
    
    def get_available_models(self, device_type: str = None) -> List[str]:
        """获取可用的设备型号"""
        if device_type:
            return [model for model, info in self.device_specs.items() 
                   if info.type == device_type]
        return list(self.device_specs.keys())

class TopologyGenerator:
    """拓扑生成器"""
    
    def __init__(self, device_db: HuaweiDeviceDatabase):
        self.device_db = device_db
        self.topo_template = """<?xml version="1.0" encoding="UNICODE" ?>
<topo version="1.3.00.100">
    <devices>
        {% for device in devices %}
        <dev id="{{ device.id }}" name="{{ device.name }}" poe="0" model="{{ device.model }}" settings="{{ device.settings }}" system_mac="{{ device.system_mac }}" com_port="{{ device.com_port }}" bootmode="1" cx="{{ device.cx }}" cy="{{ device.cy }}" edit_left="{{ device.edit_left }}" edit_top="{{ device.edit_top }}">
            <slot number="slot17" isMainBoard="1">
                {% for interface_group in device.interface_groups %}
                <interface sztype="{{ interface_group.sztype }}" interfacename="{{ interface_group.interfacename }}" count="{{ interface_group.count }}" />
                {% endfor %}
            </slot>
        </dev>
        {% endfor %}
    </devices>
    <lines>
        {% for connection in connections %}
        <line srcDeviceId="{{ connection.src_device_id }}" srcIndex="{{ connection.src_index }}" srcBoundRectIsMoved="0" srcBoundRect_X="{{ connection.src_rect_x }}" srcBoundRect_Y="{{ connection.src_rect_y }}" srcOffset_X="0.000000" srcOffset_Y="0.000000" tarDeviceId="{{ connection.dst_device_id }}" tarIndex="{{ connection.dst_index }}" tarBoundRectIsMoved="0" tarBoundRect_X="{{ connection.dst_rect_x }}" tarBoundRect_Y="{{ connection.dst_rect_y }}" tarOffset_X="0.000000" tarOffset_Y="0.000000" />
        {% endfor %}
    </lines>
    <shapes />
    <txttips />
</topo>"""
    
    def auto_assign_interfaces(self, connections: List[Dict]) -> List[Dict]:
        """自动分配接口连接"""
        device_interface_usage = {}
        assigned_connections = []
        
        for conn in connections:
            src_device = conn['src_device']
            dst_device = conn['dst_device']
            
            # 初始化设备接口使用计数
            if src_device not in device_interface_usage:
                device_interface_usage[src_device] = 0
            if dst_device not in device_interface_usage:
                device_interface_usage[dst_device] = 0
            
            # 获取设备信息
            src_info = self.device_db.get_device_info(conn.get('src_model', 'AR2220'))
            dst_info = self.device_db.get_device_info(conn.get('dst_model', 'AR2220'))
            
            # 自动分配接口
            src_interface_id = device_interface_usage[src_device]
            dst_interface_id = device_interface_usage[dst_device]
            
            # 选择合适的接口类型
            src_interface_name = self._get_interface_name(src_info, src_interface_id)
            dst_interface_name = self._get_interface_name(dst_info, dst_interface_id)
            
            assigned_connections.append({
                'src_device': src_device,
                'src_interface': src_interface_id,
                'src_interface_name': src_interface_name,
                'dst_device': dst_device,
                'dst_interface': dst_interface_id,
                'dst_interface_name': dst_interface_name
            })
            
            device_interface_usage[src_device] += 1
            device_interface_usage[dst_device] += 1
        
        return assigned_connections
    
    def _get_interface_name(self, device_info: DeviceInfo, interface_id: int) -> str:
        """根据设备信息和接口ID获取接口名称"""
        current_id = 0
        for interface_type in device_info.interfaces:
            if current_id + interface_type['count'] > interface_id:
                local_id = interface_id - current_id
                return f"{interface_type['prefix']}{local_id}"
            current_id += interface_type['count']
        
        # 如果超出范围，返回第一个接口类型
        return f"{device_info.interfaces[0]['prefix']}0"
    
    def generate_topology(self, requirement: NetworkRequirement) -> str:
        """生成拓扑XML文件"""
        template = Template(self.topo_template)
        
        # 处理设备位置（自动布局）
        devices_with_layout = self._auto_layout_devices(requirement.devices)
        
        # 自动分配接口连接
        assigned_connections = self.auto_assign_interfaces(requirement.connections)
        
        # 生成XML
        topo_xml = template.render(
            devices=devices_with_layout,
            connections=assigned_connections
        )
        
        return topo_xml
    
    def _auto_layout_devices(self, devices: List[Dict]) -> List[Dict]:
        """自动布局设备位置"""
        layout_devices = []
        x_start, y_start = 100, 100
        x_spacing, y_spacing = 200, 150
        devices_per_row = 4
        
        for i, device in enumerate(devices):
            row = i // devices_per_row
            col = i % devices_per_row
            
            device_with_layout = device.copy()
            device_with_layout.update({
                'x': x_start + col * x_spacing,
                'y': y_start + row * y_spacing,
                'label_x': x_start + col * x_spacing,
                'label_y': y_start + row * y_spacing - 30,
                'poe': 'false'
            })
            
            # 添加接口信息
            device_info = self.device_db.get_device_info(device['model'])
            if device_info:
                interfaces = []
                interface_id = 0
                for interface_type in device_info.interfaces:
                    for j in range(interface_type['count']):
                        interfaces.append({
                            'id': interface_id,
                            'name': f"{interface_type['prefix']}{j}"
                        })
                        interface_id += 1
                device_with_layout['interfaces'] = interfaces
            
            layout_devices.append(device_with_layout)
        
        return layout_devices

class ConfigGenerator:
    """配置生成器"""
    
    def __init__(self, device_db: HuaweiDeviceDatabase):
        self.device_db = device_db
        self._load_templates()
    
    def _load_templates(self):
        """加载配置模板"""
        self.router_template = Template("""
sysname {{ device_name }}
#
{% for vlan in vlans %}
vlan {{ vlan.id }}
 description {{ vlan.description }}
#
{% endfor %}
{% for interface in interfaces %}
interface {{ interface.name }}
{% if interface.description %}
 description {{ interface.description }}
{% endif %}
{% if interface.ip %}
 ip address {{ interface.ip }} {{ interface.mask }}
{% endif %}
{% if interface.vlan %}
 port link-type {{ interface.link_type | default('access') }}
{% if interface.link_type == 'trunk' %}
 port trunk allow-pass vlan {{ interface.vlans | join(' ') }}
{% else %}
 port default vlan {{ interface.vlan }}
{% endif %}
{% endif %}
 undo shutdown
#
{% endfor %}
{% if routing.protocol == 'ospf' %}
ospf {{ routing.process_id | default(1) }}
 router-id {{ routing.router_id }}
{% for area in routing.areas %}
 area {{ area.id }}
{% for network in area.networks %}
  network {{ network.address }} {{ network.wildcard }}
{% endfor %}
{% endfor %}
#
{% endif %}
{% for route in static_routes %}
ip route-static {{ route.destination }} {{ route.mask }} {{ route.next_hop }}
{% endfor %}
{% for acl in acls %}
acl number {{ acl.number }}
{% for rule in acl.rules %}
 rule {{ rule.action }} {{ rule.protocol }} {{ rule.condition }}
{% endfor %}
#
{% endfor %}
""")
        
        self.switch_template = Template("""
sysname {{ device_name }}
#
vlan batch {{ vlans | map(attribute='id') | join(' ') }}
{% for vlan in vlans %}
vlan {{ vlan.id }}
 description {{ vlan.description }}
#
{% endfor %}
{% for interface in interfaces %}
interface {{ interface.name }}
{% if interface.description %}
 description {{ interface.description }}
{% endif %}
 port link-type {{ interface.link_type | default('access') }}
{% if interface.link_type == 'trunk' %}
 port trunk allow-pass vlan {{ interface.vlans | join(' ') }}
{% else %}
 port default vlan {{ interface.vlan }}
{% endif %}
{% if interface.port_security %}
 port-security enable
 port-security max-mac-num {{ interface.max_mac | default(1) }}
 port-security mac-address sticky
{% endif %}
 undo shutdown
#
{% endfor %}
""")
    
    def generate_device_config(self, device: Dict, network_config: Dict) -> str:
        """生成设备配置"""
        device_info = self.device_db.get_device_info(device['model'])
        
        if device_info.type == 'router':
            return self.router_template.render(**network_config)
        elif device_info.type == 'switch':
            return self.switch_template.render(**network_config)
        else:
            logger.warning(f"Unsupported device type: {device_info.type}")
            return ""

class ENSPDeployer:
    """eNSP部署器"""

    def __init__(self, ensp_path: str = None):
        self.ensp_path = ensp_path or self._find_ensp_path()
        self.config_path = os.path.join(os.path.expanduser("~"), "AppData", "Local", "eNSP", "config")
        self.topo_path = os.path.join(os.path.expanduser("~"), "Documents", "eNSP", "topology")

    def _find_ensp_path(self) -> str:
        """自动查找eNSP安装路径"""
        possible_paths = [
            "C:\\Program Files\\eNSP\\eNSP.exe",
            "C:\\Program Files (x86)\\eNSP\\eNSP.exe",
            "D:\\eNSP\\eNSP.exe"
        ]

        for path in possible_paths:
            if os.path.exists(path):
                return path

        logger.warning("eNSP installation not found, please specify path manually")
        return ""

    def deploy_topology(self, topo_xml: str, topo_name: str) -> bool:
        """部署拓扑文件"""
        try:
            # 确保目录存在
            os.makedirs(self.topo_path, exist_ok=True)

            # 保存拓扑文件
            topo_file = os.path.join(self.topo_path, f"{topo_name}.topo")
            with open(topo_file, 'w', encoding='utf-8') as f:
                f.write(topo_xml)

            logger.info(f"Topology saved to: {topo_file}")
            return True

        except Exception as e:
            logger.error(f"Failed to deploy topology: {str(e)}")
            return False

    def deploy_configs(self, device_configs: Dict[str, str]) -> bool:
        """部署设备配置文件"""
        try:
            # 确保配置目录存在
            os.makedirs(self.config_path, exist_ok=True)

            for device_name, config in device_configs.items():
                config_file = os.path.join(self.config_path, f"{device_name}.cfg")
                with open(config_file, 'w', encoding='utf-8') as f:
                    f.write(config)
                logger.info(f"Config saved for {device_name}: {config_file}")

            return True

        except Exception as e:
            logger.error(f"Failed to deploy configs: {str(e)}")
            return False

    def start_ensp(self, topo_file: str = None) -> bool:
        """启动eNSP并加载拓扑"""
        try:
            if not self.ensp_path or not os.path.exists(self.ensp_path):
                logger.error("eNSP executable not found")
                return False

            # 启动eNSP
            if topo_file:
                subprocess.Popen([self.ensp_path, topo_file])
            else:
                subprocess.Popen([self.ensp_path])

            logger.info("eNSP started successfully")
            return True

        except Exception as e:
            logger.error(f"Failed to start eNSP: {str(e)}")
            return False

class NetworkTester:
    """网络测试器"""

    def __init__(self):
        self.test_results = []

    def ping_test(self, source_ip: str, target_ip: str, count: int = 3) -> Dict:
        """执行ping测试"""
        try:
            result = subprocess.run(
                ['ping', '-n', str(count), target_ip],
                capture_output=True,
                text=True,
                timeout=30
            )

            success = result.returncode == 0
            test_result = {
                'test_type': 'ping',
                'source': source_ip,
                'target': target_ip,
                'success': success,
                'output': result.stdout,
                'timestamp': time.time()
            }

            self.test_results.append(test_result)
            return test_result

        except Exception as e:
            logger.error(f"Ping test failed: {str(e)}")
            return {'success': False, 'error': str(e)}

    def connectivity_matrix_test(self, ip_list: List[str]) -> Dict:
        """执行连通性矩阵测试"""
        matrix = {}

        for source in ip_list:
            matrix[source] = {}
            for target in ip_list:
                if source != target:
                    result = self.ping_test(source, target, 1)
                    matrix[source][target] = result['success']
                else:
                    matrix[source][target] = True

        return matrix

    def generate_test_report(self) -> str:
        """生成测试报告"""
        report = "# 网络测试报告\n\n"
        report += f"测试时间: {time.strftime('%Y-%m-%d %H:%M:%S')}\n"
        report += f"总测试数: {len(self.test_results)}\n\n"

        success_count = sum(1 for test in self.test_results if test['success'])
        report += f"成功测试: {success_count}\n"
        report += f"失败测试: {len(self.test_results) - success_count}\n\n"

        report += "## 详细测试结果\n\n"
        for test in self.test_results:
            status = "✅" if test['success'] else "❌"
            report += f"{status} {test['test_type']}: {test['source']} -> {test['target']}\n"

        return report

class AutomationOrchestrator:
    """自动化编排器 - 主控制类"""

    def __init__(self):
        self.device_db = HuaweiDeviceDatabase()
        self.topo_generator = TopologyGenerator(self.device_db)
        self.config_generator = ConfigGenerator(self.device_db)
        self.deployer = ENSPDeployer()
        self.tester = NetworkTester()

    def create_network_from_requirement(self, requirement_file: str) -> bool:
        """从需求文件创建完整网络"""
        try:
            # 1. 加载需求
            with open(requirement_file, 'r', encoding='utf-8') as f:
                requirement_data = json.load(f)

            requirement = NetworkRequirement(**requirement_data)

            # 2. 生成拓扑
            logger.info("Generating topology...")
            topo_xml = self.topo_generator.generate_topology(requirement)

            # 3. 生成配置
            logger.info("Generating device configurations...")
            device_configs = {}
            for device in requirement.devices:
                config = self.config_generator.generate_device_config(
                    device,
                    requirement_data.get('network_config', {})
                )
                device_configs[device['name']] = config

            # 4. 部署到eNSP
            logger.info("Deploying to eNSP...")
            self.deployer.deploy_topology(topo_xml, requirement.topology_name)
            self.deployer.deploy_configs(device_configs)

            # 5. 启动eNSP
            topo_file = os.path.join(self.deployer.topo_path, f"{requirement.topology_name}.topo")
            self.deployer.start_ensp(topo_file)

            logger.info("Network creation completed successfully!")
            return True

        except Exception as e:
            logger.error(f"Failed to create network: {str(e)}")
            return False

    def run_automated_tests(self, test_config: Dict) -> str:
        """运行自动化测试"""
        logger.info("Starting automated tests...")

        # 等待设备启动
        startup_time = test_config.get('startup_wait', 60)
        logger.info(f"Waiting {startup_time} seconds for devices to start...")
        time.sleep(startup_time)

        # 执行连通性测试
        ip_list = test_config.get('test_ips', [])
        if ip_list:
            self.tester.connectivity_matrix_test(ip_list)

        # 生成测试报告
        report = self.tester.generate_test_report()

        # 保存报告
        report_file = f"test_report_{int(time.time())}.md"
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(report)

        logger.info(f"Test report saved to: {report_file}")
        return report_file

def main():
    """主函数 - 示例用法"""
    orchestrator = AutomationOrchestrator()

    # 示例：创建校园网拓扑
    campus_requirement = {
        "topology_name": "campus_network",
        "devices": [
            {"id": "1", "name": "AR1", "model": "AR2220"},
            {"id": "2", "name": "AR2", "model": "AR2220"},
            {"id": "3", "name": "SW1", "model": "S5700"},
            {"id": "4", "name": "SW2", "model": "S5700"},
            {"id": "5", "name": "FW", "model": "USG6000V"}
        ],
        "vlans": [
            {"id": 10, "description": "Teaching Building"},
            {"id": 20, "description": "Yifu Building"},
            {"id": 30, "description": "Study Room"},
            {"id": 40, "description": "Reading Room"}
        ],
        "routing_protocol": "ospf",
        "security_features": ["acl", "nat"],
        "connections": [
            {"src_device": "1", "dst_device": "3", "src_model": "AR2220", "dst_model": "S5700"},
            {"src_device": "2", "dst_device": "4", "src_model": "AR2220", "dst_model": "S5700"},
            {"src_device": "1", "dst_device": "5", "src_model": "AR2220", "dst_model": "USG6000V"},
            {"src_device": "2", "dst_device": "5", "src_model": "AR2220", "dst_model": "USG6000V"}
        ],
        "network_config": {
            "device_name": "AR1",
            "vlans": [{"id": 10, "description": "Teaching"}],
            "interfaces": [
                {
                    "name": "GigabitEthernet0/0/1",
                    "ip": "*********",
                    "mask": "*************",
                    "description": "To SW1"
                }
            ],
            "routing": {
                "protocol": "ospf",
                "router_id": "*******",
                "areas": [{"id": "0.0.0.0", "networks": [{"address": "*********", "wildcard": "*********"}]}]
            },
            "static_routes": [],
            "acls": []
        }
    }

    # 保存需求文件
    with open('campus_requirement.json', 'w', encoding='utf-8') as f:
        json.dump(campus_requirement, f, indent=2, ensure_ascii=False)

    # 创建网络
    success = orchestrator.create_network_from_requirement('campus_requirement.json')

    if success:
        # 运行测试
        test_config = {
            "startup_wait": 60,
            "test_ips": ["*********", "*********", "*********", "*********"]
        }
        orchestrator.run_automated_tests(test_config)

if __name__ == "__main__":
    main()
