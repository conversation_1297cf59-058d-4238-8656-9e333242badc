#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基于真实eNSP格式的拓扑生成器
根据实际eNSP拖拽后的文件格式来生成正确的拓扑文件
"""

import json
import uuid
import random

class RealENSPGenerator:
    """真实eNSP格式的拓扑生成器"""
    
    def __init__(self):
        self.device_models = {
            'router': {
                'AR1220': {
                    'interfaces': [
                        {'sztype': 'Ethernet', 'interfacename': 'GE', 'count': 2},
                        {'sztype': 'Ethernet', 'interfacename': 'Ethernet', 'count': 8}
                    ]
                },
                'AR2220': {
                    'interfaces': [
                        {'sztype': 'Ethernet', 'interfacename': 'GE', 'count': 4},
                        {'sztype': 'Ethernet', 'interfacename': 'Ethernet', 'count': 2}
                    ]
                }
            },
            'switch': {
                'S3700': {
                    'interfaces': [
                        {'sztype': 'Ethernet', 'interfacename': 'Ethernet', 'count': 24},
                        {'sztype': 'Ethernet', 'interfacename': 'GE', 'count': 4}
                    ]
                },
                'S5700': {
                    'interfaces': [
                        {'sztype': 'Ethernet', 'interfacename': 'GE', 'count': 24},
                        {'sztype': 'Ethernet', 'interfacename': '10GE', 'count': 4}
                    ]
                }
            },
            'pc': {
                'PC': {
                    'interfaces': [
                        {'sztype': 'Ethernet', 'interfacename': 'Ethernet', 'count': 1}
                    ]
                }
            }
        }
    
    def generate_device_id(self):
        """生成设备ID"""
        return str(uuid.uuid4()).upper()
    
    def generate_mac_address(self, device_type='default'):
        """生成MAC地址"""
        if device_type == 'pc':
            # PC使用50-50-50开头
            return f"50-50-50-{random.randint(10,99):02X}-{random.randint(10,99):02X}-{random.randint(1,99):02X}"
        elif device_type == 'router':
            # 路由器使用00-E0-FC开头
            return f"00-E0-FC-{random.randint(10,99):02X}-{random.randint(10,99):02X}-{random.randint(1,99):02X}"
        elif device_type == 'switch':
            # 交换机使用4C-1F-CC开头
            return f"4C-1F-CC-{random.randint(10,99):02X}-{random.randint(10,99):02X}-{random.randint(1,99):02X}"
        else:
            return f"00-00-00-{random.randint(10,99):02X}-{random.randint(10,99):02X}-{random.randint(1,99):02X}"
    
    def generate_pc_settings(self, ip, mask, gateway, mac):
        """生成PC设置字符串"""
        return f" -simpc_ip {ip} -simpc_mask {mask} -simpc_gateway {gateway} -simpc_mac {mac} -simpc_mc_dstip 0.0.0.0 -simpc_mc_dstmac 00-00-00-00-00-00 -simpc_dns1 0.0.0.0 -simpc_dns2 0.0.0.0 -simpc_ipv6 :: -simpc_prefix 128 -simpc_gatewayv6 :: -simpc_dhcp_state 0 -simpc_dhcpv6_state 0 -simpc_dns_auto_state 0 -simpc_igmp_version 1 -simpc_group_ip_start 0.0.0.0 -simpc_src_ip_start 0.0.0.0 -simpc_group_num 0 -simpc_group_step 0 -simpc_src_num 0 -simpc_src_step 0 -simpc_type MODE_IS_INCLUDE "
    
    def create_device(self, name, model, device_type, x, y, com_port, pc_config=None):
        """创建设备"""
        device_id = self.generate_device_id()
        mac = self.generate_mac_address(device_type)
        
        # 计算edit位置
        edit_left = int(x) + 27
        edit_top = int(y) + 54
        
        # 获取接口配置
        if device_type in self.device_models and model in self.device_models[device_type]:
            interfaces = self.device_models[device_type][model]['interfaces']
        else:
            interfaces = [{'sztype': 'Ethernet', 'interfacename': 'Ethernet', 'count': 1}]
        
        # 生成设置字符串
        settings = ""
        if device_type == 'pc' and pc_config:
            settings = self.generate_pc_settings(
                pc_config['ip'], 
                pc_config['mask'], 
                pc_config['gateway'], 
                mac
            )
        
        device = {
            'id': device_id,
            'name': name,
            'model': model,
            'settings': settings,
            'system_mac': mac,
            'com_port': str(com_port),
            'cx': f"{x:.6f}",
            'cy': f"{y:.6f}",
            'edit_left': str(edit_left),
            'edit_top': str(edit_top),
            'interface_groups': interfaces
        }
        
        return device
    
    def create_connection(self, src_device, src_index, dst_device, dst_index):
        """创建连接"""
        return {
            'src_device_id': src_device['id'],
            'src_index': str(src_index),
            'src_rect_x': f"{float(src_device['edit_left']):.6f}",
            'src_rect_y': f"{float(src_device['edit_top']):.6f}",
            'dst_device_id': dst_device['id'],
            'dst_index': str(dst_index),
            'dst_rect_x': f"{float(dst_device['edit_left']):.6f}",
            'dst_rect_y': f"{float(dst_device['edit_top']):.6f}"
        }
    
    def generate_simple_network(self):
        """生成简单网络拓扑"""
        devices = []
        connections = []
        
        # 创建设备
        r1 = self.create_device("R1", "AR1220", "router", 500, 200, 2000)
        sw1 = self.create_device("SW1", "S3700", "switch", 300, 400, 2001)
        sw2 = self.create_device("SW2", "S3700", "switch", 700, 400, 2002)
        
        pc1 = self.create_device("PC1", "PC", "pc", 200, 600, 2003, 
                                {'ip': '*************', 'mask': '*************', 'gateway': '************'})
        pc2 = self.create_device("PC2", "PC", "pc", 400, 600, 2004,
                                {'ip': '************1', 'mask': '*************', 'gateway': '************'})
        pc3 = self.create_device("PC3", "PC", "pc", 600, 600, 2005,
                                {'ip': '************0', 'mask': '*************', 'gateway': '************'})
        pc4 = self.create_device("PC4", "PC", "pc", 800, 600, 2006,
                                {'ip': '************1', 'mask': '*************', 'gateway': '************'})
        
        devices = [r1, sw1, sw2, pc1, pc2, pc3, pc4]
        
        # 创建连接
        # R1 GE0 -> SW1 GE24 (index 24)
        connections.append(self.create_connection(r1, 0, sw1, 24))
        # R1 GE1 -> SW2 GE24 (index 24)  
        connections.append(self.create_connection(r1, 1, sw2, 24))
        # PC1 -> SW1 Eth0 (index 0)
        connections.append(self.create_connection(pc1, 0, sw1, 0))
        # PC2 -> SW1 Eth1 (index 1)
        connections.append(self.create_connection(pc2, 0, sw1, 1))
        # PC3 -> SW2 Eth0 (index 0)
        connections.append(self.create_connection(pc3, 0, sw2, 0))
        # PC4 -> SW2 Eth1 (index 1)
        connections.append(self.create_connection(pc4, 0, sw2, 1))
        
        return devices, connections
    
    def generate_topo_xml(self, devices, connections):
        """生成拓扑XML"""
        xml_content = '<?xml version="1.0" encoding="UNICODE" ?>\n'
        xml_content += '<topo version="1.3.00.100">\n'
        xml_content += '    <devices>\n'
        
        # 添加设备
        for device in devices:
            xml_content += f'        <dev id="{device["id"]}" name="{device["name"]}" poe="0" model="{device["model"]}" settings="{device["settings"]}" system_mac="{device["system_mac"]}" com_port="{device["com_port"]}" bootmode="1" cx="{device["cx"]}" cy="{device["cy"]}" edit_left="{device["edit_left"]}" edit_top="{device["edit_top"]}">\n'
            xml_content += '            <slot number="slot17" isMainBoard="1">\n'
            
            for interface_group in device['interface_groups']:
                xml_content += f'                <interface sztype="{interface_group["sztype"]}" interfacename="{interface_group["interfacename"]}" count="{interface_group["count"]}" />\n'
            
            xml_content += '            </slot>\n'
            xml_content += '        </dev>\n'
        
        xml_content += '    </devices>\n'
        xml_content += '    <lines>\n'
        
        # 添加连接
        for conn in connections:
            xml_content += f'        <line srcDeviceId="{conn["src_device_id"]}" srcIndex="{conn["src_index"]}" srcBoundRectIsMoved="0" srcBoundRect_X="{conn["src_rect_x"]}" srcBoundRect_Y="{conn["src_rect_y"]}" srcOffset_X="0.000000" srcOffset_Y="0.000000" tarDeviceId="{conn["dst_device_id"]}" tarIndex="{conn["dst_index"]}" tarBoundRectIsMoved="0" tarBoundRect_X="{conn["dst_rect_x"]}" tarBoundRect_Y="{conn["dst_rect_y"]}" tarOffset_X="0.000000" tarOffset_Y="0.000000" />\n'
        
        xml_content += '    </lines>\n'
        xml_content += '    <shapes />\n'
        xml_content += '    <txttips />\n'
        xml_content += '</topo>\n'
        
        return xml_content

def main():
    """主函数"""
    print("🚀 生成真实eNSP格式的拓扑文件...")
    
    generator = RealENSPGenerator()
    devices, connections = generator.generate_simple_network()
    topo_xml = generator.generate_topo_xml(devices, connections)
    
    # 保存拓扑文件
    with open('real_format_demo.topo', 'w', encoding='utf-8') as f:
        f.write(topo_xml)
    
    print("✅ 拓扑文件生成完成: real_format_demo.topo")
    
    # 生成设备信息报告
    print("\n📋 生成的设备信息:")
    for device in devices:
        print(f"- {device['name']} ({device['model']}): {device['system_mac']}")
    
    print(f"\n🔗 连接数量: {len(connections)}")
    print("\n💡 使用说明:")
    print("1. 将 real_format_demo.topo 复制到 eNSP topology 目录")
    print("2. 将对应的 .cfg 文件复制到 eNSP config 目录")
    print("3. 在 eNSP 中打开拓扑文件")

if __name__ == "__main__":
    main()
