#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
eNSP网络自动化工具演示脚本
展示如何使用自动化工具创建校园网拓扑
"""

import json
import os
from datetime import datetime

def create_demo_requirement():
    """创建演示用的网络需求"""
    demo_requirement = {
        "topology_name": "demo_campus_network",
        "description": "演示用校园网拓扑",
        "devices": [
            {
                "id": "1",
                "name": "Core-R1",
                "model": "AR2220",
                "type": "router",
                "role": "核心路由器1",
                "management_ip": "**********"
            },
            {
                "id": "2",
                "name": "Core-R2", 
                "model": "AR2220",
                "type": "router",
                "role": "核心路由器2",
                "management_ip": "**********"
            },
            {
                "id": "3",
                "name": "Firewall",
                "model": "USG6000V",
                "type": "firewall",
                "role": "安全网关",
                "management_ip": "**********"
            },
            {
                "id": "4",
                "name": "Agg-SW1",
                "model": "S5700",
                "type": "switch",
                "role": "汇聚交换机1",
                "management_ip": "**********"
            },
            {
                "id": "5",
                "name": "Agg-SW2",
                "model": "S5700", 
                "type": "switch",
                "role": "汇聚交换机2",
                "management_ip": "**********"
            }
        ],
        "vlans": [
            {
                "id": 10,
                "name": "teaching",
                "description": "教学楼VLAN",
                "network": "*********/24",
                "gateway": "*********"
            },
            {
                "id": 20,
                "name": "office",
                "description": "办公楼VLAN", 
                "network": "*********/24",
                "gateway": "*********"
            },
            {
                "id": 30,
                "name": "student",
                "description": "学生宿舍VLAN",
                "network": "*********/24",
                "gateway": "*********"
            },
            {
                "id": 100,
                "name": "management",
                "description": "管理VLAN",
                "network": "**********/24",
                "gateway": "**********"
            }
        ],
        "routing_protocol": "ospf",
        "security_features": ["acl", "nat", "firewall"],
        "connections": [
            {
                "description": "Core-R1 连接 Agg-SW1",
                "src_device": "1",
                "dst_device": "4",
                "link_type": "trunk"
            },
            {
                "description": "Core-R2 连接 Agg-SW2",
                "src_device": "2", 
                "dst_device": "5",
                "link_type": "trunk"
            },
            {
                "description": "Core-R1 连接 Firewall",
                "src_device": "1",
                "dst_device": "3",
                "link_type": "routed"
            },
            {
                "description": "Core-R2 连接 Firewall",
                "src_device": "2",
                "dst_device": "3", 
                "link_type": "routed"
            }
        ]
    }
    
    return demo_requirement

def generate_demo_topology_xml():
    """生成演示拓扑XML"""
    xml_content = '''<?xml version="1.0" encoding="UTF-8"?>
<topo version="**********">
    <devices>
        <device id="1" name="Core-R1" poe="false" model="AR2220" x="200" y="100" label_x="200" label_y="70">
            <interface id="0" name="GigabitEthernet0/0/0" />
            <interface id="1" name="GigabitEthernet0/0/1" />
            <interface id="2" name="GigabitEthernet0/0/2" />
            <interface id="3" name="GigabitEthernet0/0/3" />
        </device>
        <device id="2" name="Core-R2" poe="false" model="AR2220" x="400" y="100" label_x="400" label_y="70">
            <interface id="0" name="GigabitEthernet0/0/0" />
            <interface id="1" name="GigabitEthernet0/0/1" />
            <interface id="2" name="GigabitEthernet0/0/2" />
            <interface id="3" name="GigabitEthernet0/0/3" />
        </device>
        <device id="3" name="Firewall" poe="false" model="USG6000V" x="300" y="200" label_x="300" label_y="170">
            <interface id="0" name="GigabitEthernet1/0/0" />
            <interface id="1" name="GigabitEthernet1/0/1" />
            <interface id="2" name="GigabitEthernet1/0/2" />
            <interface id="3" name="GigabitEthernet1/0/3" />
        </device>
        <device id="4" name="Agg-SW1" poe="false" model="S5700" x="100" y="300" label_x="100" label_y="270">
            <interface id="0" name="GigabitEthernet0/0/1" />
            <interface id="1" name="GigabitEthernet0/0/2" />
            <interface id="2" name="GigabitEthernet0/0/3" />
            <interface id="23" name="GigabitEthernet0/0/24" />
        </device>
        <device id="5" name="Agg-SW2" poe="false" model="S5700" x="500" y="300" label_x="500" label_y="270">
            <interface id="0" name="GigabitEthernet0/0/1" />
            <interface id="1" name="GigabitEthernet0/0/2" />
            <interface id="2" name="GigabitEthernet0/0/3" />
            <interface id="23" name="GigabitEthernet0/0/24" />
        </device>
    </devices>
    <lines>
        <line srcDeviceId="1" srcInterfaceId="0" dstDeviceId="4" dstInterfaceId="23" />
        <line srcDeviceId="2" srcInterfaceId="0" dstDeviceId="5" dstInterfaceId="23" />
        <line srcDeviceId="1" srcInterfaceId="1" dstDeviceId="3" dstInterfaceId="0" />
        <line srcDeviceId="2" srcInterfaceId="1" dstDeviceId="3" dstInterfaceId="1" />
    </lines>
</topo>'''
    
    return xml_content

def generate_demo_router_config():
    """生成演示路由器配置"""
    config = '''#
# Core-R1 路由器配置
# 生成时间: ''' + datetime.now().strftime('%Y-%m-%d %H:%M:%S') + '''
#
sysname Core-R1
#
# 系统配置
clock timezone China-Standard-Time add 08:00:00
#
# 用户配置
aaa
 local-user admin password irreversible-cipher Huawei@123
 local-user admin privilege level 15
 local-user admin service-type telnet ssh http
#
# SSH配置
ssh server enable
ssh server-source all-interface
#
# VLAN配置
vlan 10
 description Teaching Building VLAN
#
vlan 20
 description Office Building VLAN
#
vlan 100
 description Management VLAN
#
# 接口配置
interface GigabitEthernet0/0/0
 description To Agg-SW1 - Trunk
 port link-type trunk
 port trunk allow-pass vlan 10 20 100
 undo shutdown
#
interface GigabitEthernet0/0/1
 description To Firewall - Routed
 ip address ********** ***************
 undo shutdown
#
interface Vlanif10
 description Teaching Building Gateway
 ip address ********* *************
 dhcp select interface
#
interface Vlanif20
 description Office Building Gateway
 ip address ********* *************
 dhcp select interface
#
interface Vlanif100
 description Management Gateway
 ip address ********** *************
#
# DHCP配置
dhcp enable
ip pool teaching_pool
 gateway-list *********
 network ********* mask *************
 dns-list ******* ***************
 lease day 1
#
ip pool office_pool
 gateway-list *********
 network ********* mask *************
 dns-list ******* ***************
 lease day 1
#
# OSPF配置
ospf 1
 router-id *******
 area 0.0.0.0
  network ********* *********
  network ********* *********
  network ********** *******
#
# 静态路由
ip route-static 0.0.0.0 0.0.0.0 **********
#
# ACL配置
acl number 3000
 description Block Teaching to Management
 rule 5 deny ip source ********* ********* destination ********** *********
 rule 10 permit ip
#
return
'''
    
    return config

def generate_demo_switch_config():
    """生成演示交换机配置"""
    config = '''#
# Agg-SW1 交换机配置
# 生成时间: ''' + datetime.now().strftime('%Y-%m-%d %H:%M:%S') + '''
#
sysname Agg-SW1
#
# 系统配置
clock timezone China-Standard-Time add 08:00:00
#
# 用户配置
aaa
 local-user admin password irreversible-cipher Huawei@123
 local-user admin privilege level 15
 local-user admin service-type telnet ssh http
#
# SSH配置
ssh server enable
ssh server-source all-interface
#
# VLAN批量创建
vlan batch 10 20 100
vlan 10
 description Teaching Building VLAN
#
vlan 20
 description Office Building VLAN
#
vlan 100
 description Management VLAN
#
# 接口配置
interface GigabitEthernet0/0/1
 description Teaching Building PC
 port link-type access
 port default vlan 10
 port-security enable
 port-security max-mac-num 1
 port-security mac-address sticky
 undo shutdown
#
interface GigabitEthernet0/0/2
 description Office Building PC
 port link-type access
 port default vlan 20
 port-security enable
 port-security max-mac-num 1
 port-security mac-address sticky
 undo shutdown
#
interface GigabitEthernet0/0/24
 description Uplink to Core-R1
 port link-type trunk
 port trunk allow-pass vlan 10 20 100
 undo shutdown
#
# STP配置
stp mode rstp
stp priority 4096
#
# DHCP Snooping配置
dhcp snooping enable
dhcp snooping enable vlan 10 20
interface GigabitEthernet0/0/24
 dhcp snooping trusted
#
return
'''
    
    return config

def create_demo_files():
    """创建演示文件"""
    print("🚀 创建eNSP网络自动化演示文件...")
    
    # 1. 创建需求文件
    requirement = create_demo_requirement()
    with open('demo_requirement.json', 'w', encoding='utf-8') as f:
        json.dump(requirement, f, indent=2, ensure_ascii=False)
    print("✅ 创建需求文件: demo_requirement.json")
    
    # 2. 创建拓扑文件
    topo_xml = generate_demo_topology_xml()
    with open('demo_campus_network.topo', 'w', encoding='utf-8') as f:
        f.write(topo_xml)
    print("✅ 创建拓扑文件: demo_campus_network.topo")
    
    # 3. 创建路由器配置
    router_config = generate_demo_router_config()
    with open('Core-R1.cfg', 'w', encoding='utf-8') as f:
        f.write(router_config)
    print("✅ 创建路由器配置: Core-R1.cfg")
    
    # 4. 创建交换机配置
    switch_config = generate_demo_switch_config()
    with open('Agg-SW1.cfg', 'w', encoding='utf-8') as f:
        f.write(switch_config)
    print("✅ 创建交换机配置: Agg-SW1.cfg")
    
    # 5. 创建使用说明
    usage_guide = '''# eNSP网络自动化演示使用指南

## 生成的文件说明

1. **demo_requirement.json** - 网络需求配置文件
   - 定义了设备、VLAN、连接关系等
   - 可以根据实际需求修改

2. **demo_campus_network.topo** - eNSP拓扑文件
   - 可直接在eNSP中打开
   - 包含设备布局和连接关系

3. **Core-R1.cfg** - 核心路由器配置文件
   - 包含VLAN、接口、OSPF、DHCP等配置
   - 复制到eNSP配置目录即可使用

4. **Agg-SW1.cfg** - 汇聚交换机配置文件
   - 包含VLAN、端口安全、STP等配置
   - 复制到eNSP配置目录即可使用

## 使用步骤

### 方法1: 手动部署
1. 将 .topo 文件复制到: `%USERPROFILE%\\Documents\\eNSP\\topology\\`
2. 将 .cfg 文件复制到: `%USERPROFILE%\\AppData\\Local\\eNSP\\config\\`
3. 启动eNSP并打开拓扑文件
4. 启动所有设备

### 方法2: 自动化部署（需要Python环境）
```bash
# 安装依赖
pip install jinja2

# 运行自动化脚本
python enhanced_automation.py
```

## 网络拓扑说明

```
                    Internet
                        |
                   [Firewall]
                   /        \\
            [Core-R1]    [Core-R2]
                |            |
           [Agg-SW1]    [Agg-SW2]
              |            |
        Teaching PCs   Office PCs
```

## IP地址规划

- 教学楼VLAN10: *********/24
- 办公楼VLAN20: *********/24
- 学生宿舍VLAN30: *********/24
- 管理VLAN100: **********/24

## 测试验证

1. **连通性测试**
   ```
   ping *********  # 教学楼网关
   ping *********  # 办公楼网关
   ```

2. **路由表检查**
   ```
   display ip routing-table
   display ospf peer
   ```

3. **VLAN验证**
   ```
   display vlan
   display port vlan
   ```

## 故障排除

1. **设备启动失败**
   - 检查eNSP版本兼容性
   - 确认系统资源充足

2. **配置加载失败**
   - 检查配置文件语法
   - 确认文件路径正确

3. **网络不通**
   - 检查接口状态
   - 验证路由配置
   - 确认VLAN配置

## 扩展功能

- 添加更多设备类型（防火墙、无线设备等）
- 实现动态路由协议（BGP、ISIS等）
- 集成安全策略（ACL、NAT等）
- 添加网络监控和性能测试

---
生成时间: ''' + datetime.now().strftime('%Y-%m-%d %H:%M:%S') + '''
'''
    
    with open('使用指南.md', 'w', encoding='utf-8') as f:
        f.write(usage_guide)
    print("✅ 创建使用指南: 使用指南.md")
    
    print("\n🎉 演示文件创建完成!")
    print("\n📁 生成的文件:")
    print("   - demo_requirement.json (网络需求)")
    print("   - demo_campus_network.topo (eNSP拓扑)")
    print("   - Core-R1.cfg (路由器配置)")
    print("   - Agg-SW1.cfg (交换机配置)")
    print("   - 使用指南.md (详细说明)")
    
    print("\n🔧 下一步操作:")
    print("   1. 查看 '使用指南.md' 了解详细使用方法")
    print("   2. 将拓扑文件导入eNSP")
    print("   3. 将配置文件复制到eNSP配置目录")
    print("   4. 启动设备并测试网络连通性")

if __name__ == "__main__":
    create_demo_files()
