<?xml version="1.0" encoding="UTF-8"?>
<topo version="1.3.00.100">
    <devices>
        <device id="1" name="R1" poe="false" model="AR2220" x="300" y="100" label_x="300" label_y="70">
            <interface id="0" name="GigabitEthernet0/0/0" />
            <interface id="1" name="GigabitEthernet0/0/1" />
        </device>
        <device id="2" name="SW1" poe="false" model="S5700" x="200" y="250" label_x="200" label_y="220">
            <interface id="0" name="GigabitEthernet0/0/1" />
            <interface id="1" name="GigabitEthernet0/0/2" />
            <interface id="2" name="GigabitEthernet0/0/24" />
        </device>
        <device id="3" name="SW2" poe="false" model="S5700" x="400" y="250" label_x="400" label_y="220">
            <interface id="0" name="GigabitEthernet0/0/1" />
            <interface id="1" name="GigabitEthernet0/0/2" />
            <interface id="2" name="GigabitEthernet0/0/24" />
        </device>
        <device id="4" name="PC1" poe="false" model="PC" x="150" y="350" label_x="150" label_y="320">
            <interface id="0" name="Ethernet0/0/1" />
        </device>
        <device id="5" name="PC2" poe="false" model="PC" x="250" y="350" label_x="250" label_y="320">
            <interface id="0" name="Ethernet0/0/1" />
        </device>
        <device id="6" name="PC3" poe="false" model="PC" x="350" y="350" label_x="350" label_y="320">
            <interface id="0" name="Ethernet0/0/1" />
        </device>
        <device id="7" name="PC4" poe="false" model="PC" x="450" y="350" label_x="450" label_y="320">
            <interface id="0" name="Ethernet0/0/1" />
        </device>
    </devices>
    <lines>
        <!-- R1 to SW1 -->
        <line srcDeviceId="1" srcInterfaceId="0" dstDeviceId="2" dstInterfaceId="2" />
        <!-- R1 to SW2 -->
        <line srcDeviceId="1" srcInterfaceId="1" dstDeviceId="3" dstInterfaceId="2" />
        <!-- PC1 to SW1 -->
        <line srcDeviceId="4" srcInterfaceId="0" dstDeviceId="2" dstInterfaceId="0" />
        <!-- PC2 to SW1 -->
        <line srcDeviceId="5" srcInterfaceId="0" dstDeviceId="2" dstInterfaceId="1" />
        <!-- PC3 to SW2 -->
        <line srcDeviceId="6" srcInterfaceId="0" dstDeviceId="3" dstInterfaceId="0" />
        <!-- PC4 to SW2 -->
        <line srcDeviceId="7" srcInterfaceId="0" dstDeviceId="3" dstInterfaceId="1" />
    </lines>
</topo>
