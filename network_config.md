# eNSP校园网拓扑全配置指令文档

> 本文档基于所给拓扑结构，重新规划IP地址，适用于eNSP模拟器。包含所有路由器、交换机、防火墙、无线设备、服务器等的详细配置，涵盖VLAN、端口安全、IP分配、路由等。

# 网络拓扑结构规划

本网络拓扑结构如下：

- **核心区域**：
  - 两台核心路由器（AR1、AR2）分别负责不同楼宇和无线接入的三层网关。
  - 一台防火墙（FW）作为内外网、DMZ区的安全隔离与策略控制。
  - 一台ISP路由器（ISP-R）作为互联网出口。

- **接入区域**：
  - 两台汇聚交换机（SW1、SW2）分别负责教学楼、逸夫楼、自习室、阅览室等不同VLAN的接入。
  - 各楼宇PC、实验室PC、无线终端通过接入交换机划分到不同VLAN。

- **无线区域**：
  - 无线控制器（AC）统一管理AP。
  - AP通过有线方式接入SW1，覆盖无线终端。

- **DMZ区**：
  - 服务器交换机连接DNS、FTP等服务器，划分为DMZ专用VLAN。
  - DMZ区通过防火墙与内网、外网隔离。

- **互联网区**：
  - 互联网交换机连接ISP-R和互联网终端。

- **VLAN划分**：
  - 教学楼（VLAN10）、逸夫楼（VLAN20）、自习室（VLAN30）、阅览室（VLAN40）、无线（VLAN50）、DMZ（VLAN60）、管理（VLAN100）、业务（VLAN101）。

- **主要链路说明**：
  - AR1/AR2与SW1/SW2、FW互联，形成冗余与负载均衡。
  - 各交换机之间通过trunk链路互通所有VLAN。
  - 防火墙连接内网、DMZ、外网，统一安全策略。
  - 无线AP通过SW1接入，AC统一管理。

> 具体物理与逻辑连接可参考原始拓扑图，或根据本结构进行eNSP仿真搭建。

---

## 一、IP地址与VLAN规划

| 区域         | 网段             | VLAN ID |
|--------------|------------------|---------|
| 教学楼       | *********/24     | 10      |
| 逸夫楼       | *********/24     | 20      |
| 自习室       | *********/24     | 30      |
| 阅览室       | *********/24     | 40      |
| 无线         | *********/24     | 50      |
| DMZ区        | ********/24      | 60      |
| 管理VLAN     | **********/24    | 100     |
| 业务VLAN     | **********/24    | 101     |
| 路由互联     | **********/30    | -       |

---

## 二、设备命名与接口分配

- AR1：教学楼、逸夫楼、无线、FW互联
- AR2：自习室、阅览室、FW互联
- FW：防火墙，连接AR1、AR2、DMZ、ISP
- SW1：接入教学楼、逸夫楼、无线、AR1、AR2
- SW2：接入自习室、阅览室、AR1、AR2
- ISP-R：互联网出口
- AC/AP：无线控制与接入
- 服务器交换机：DMZ区
- 互联网交换机：ISP区

---

## 三、路由器配置（以华为AR系列为例）

### 1. AR1 配置
```shell
sysname AR1
# 教学楼VLAN10
interface GigabitEthernet0/0/1
 port link-type trunk
 port trunk allow-pass vlan 10 20 50 100 101
 undo shutdown
# 连接SW1
interface GigabitEthernet0/0/2
 ip address ********* *************
 undo shutdown
# 连接FW
interface GigabitEthernet0/0/3
 ip address ********** ***************
 undo shutdown
# 连接无线AP
interface GigabitEthernet0/0/4
 ip address ********* *************
 undo shutdown
# 静态路由
ip route-static 0.0.0.0 0.0.0.0 **********
```

### 2. AR2 配置
```shell
sysname AR2
# 自习室VLAN30
interface GigabitEthernet0/0/1
 port link-type trunk
 port trunk allow-pass vlan 30 40 100 101
 undo shutdown
# 连接SW2
interface GigabitEthernet0/0/2
 ip address ********* *************
 undo shutdown
# 连接FW
interface GigabitEthernet0/0/3
 ip address ********** ***************
 undo shutdown
# 静态路由
ip route-static 0.0.0.0 0.0.0.0 **********
```

### 3. ISP-R 配置
```shell
sysname ISP-R
interface GigabitEthernet0/0/0
 ip address ********** ***************
 undo shutdown
# 连接FW
interface GigabitEthernet0/0/1
 ip address **********0 ***************
 undo shutdown
# 默认路由
ip route-static 0.0.0.0 0.0.0.0 **********
```

---

## 四、防火墙（FW）配置
```shell
sysname FW
# 内网接口
interface GigabitEthernet0/0/0
 ip address ********** ***************
 undo shutdown
# AR2互联
interface GigabitEthernet0/0/1
 ip address ********** ***************
 undo shutdown
# DMZ区
interface GigabitEthernet0/0/2
 ip address ******** *************
 undo shutdown
# ISP互联
interface GigabitEthernet0/0/3
 ip address **********0 ***************
 undo shutdown
# 安全策略、NAT等（略，根据实际需求配置）
```

---

## 五、交换机配置（以SW1为例）

### 1. SW1 配置
```shell
sysname SW1
vlan batch 10 20 50 100 101
# 教学楼PC
interface GigabitEthernet0/0/1
 port link-type access
 port default vlan 10
 port-security enable
 port-security max-mac-num 1
 port-security mac-address sticky
# 逸夫楼PC
interface GigabitEthernet0/0/2
 port link-type access
 port default vlan 20
 port-security enable
 port-security max-mac-num 1
 port-security mac-address sticky
# 无线AP
interface GigabitEthernet0/0/3
 port link-type access
 port default vlan 50
# 上联AR1
interface GigabitEthernet0/0/24
 port link-type trunk
 port trunk allow-pass vlan 10 20 50 100 101
```

### 2. SW2 配置
```shell
sysname SW2
vlan batch 30 40 100 101
# 自习室PC
interface GigabitEthernet0/0/1
 port link-type access
 port default vlan 30
 port-security enable
 port-security max-mac-num 1
 port-security mac-address sticky
# 阅览室PC
interface GigabitEthernet0/0/2
 port link-type access
 port default vlan 40
 port-security enable
 port-security max-mac-num 1
 port-security mac-address sticky
# 上联AR2
interface GigabitEthernet0/0/24
 port link-type trunk
 port trunk allow-pass vlan 30 40 100 101
```

---

## 六、无线AP与AC配置

### 1. AP配置
```shell
sysname AP
interface WLAN-BSS1
 ssid campus_wifi
 security-profile abc123456
interface GigabitEthernet0/0/1
 ip address ********* *************
 undo shutdown
```

### 2. AC配置（简要）
```shell
sysname AC
interface GigabitEthernet0/0/1
 ip address ********* *************
 undo shutdown
# 配置WLAN管理等（略）
```

---

## 七、服务器交换机与互联网交换机

### 1. 服务器交换机（DMZ区）
```shell
sysname Server-SW
vlan batch 60 100 101
interface GigabitEthernet0/0/1
 port link-type access
 port default vlan 60
# 连接DNS/FTP服务器
interface GigabitEthernet0/0/2
 port link-type access
 port default vlan 60
```

### 2. 互联网交换机
```shell
sysname Internet-SW
vlan batch 100 101
interface GigabitEthernet0/0/1
 port link-type access
 port default vlan 100
interface GigabitEthernet0/0/2
 port link-type access
 port default vlan 101
```

---

## 八、终端与服务器配置（示例）

### 1. PC配置（以教学楼PC为例）
- IP地址：**********
- 子网掩码：*************
- 网关：*********

### 2. DNS/FTP服务器配置
- IP地址：*********/*********
- 子网掩码：*************
- 网关：********

---

## 九、端口安全配置说明
- 各接入端口均启用端口安全，限制最大MAC数为1，启用sticky MAC。
- 干道口为trunk，不启用端口安全。

---

## 十、路由与互通说明
- 各路由器间通过静态路由或OSPF实现互通。
- 防火墙根据实际需求配置安全策略与NAT。

---

## 十一、动态路由（OSPF）详细配置

### 1. AR1 OSPF配置
```shell
ospf 1
 router-id *******
 area 0.0.0.0
  network ********* *********
  network ********* *********
  network ********* *********
  network ********** *******
```

### 2. AR2 OSPF配置
```shell
ospf 1
 router-id *******
 area 0.0.0.0
  network ********* *********
  network ********* *********
  network ********** *******
```

### 3. FW OSPF配置
```shell
ospf 1
 router-id *******
 area 0.0.0.0
  network ********** *******
  network ********** *******
  network ******** *********
```

### 4. ISP-R OSPF配置
```shell
ospf 1
 router-id *******
 area 0.0.0.0
  network ********** *******
```

---

## 十二、ACL（访问控制列表）详细配置

### 1. 禁止实验楼PC访问DMZ区
```shell
acl number 3000
 rule deny ip source ********* ********* destination ******** *********
 rule permit ip
interface GigabitEthernet0/0/3  # 连接FW的接口
 packet-filter 3000 inbound
```

### 2. 允许管理VLAN远程管理
```shell
acl number 3001
 rule permit ip source ********** *********
 rule deny ip
user-interface vty 0 4
 acl 3001 inbound
```

### 3. 只允许无线终端访问互联网
```shell
acl number 3002
 rule permit ip source ********* ********* destination any
 rule deny ip
interface GigabitEthernet0/0/3  # 连接FW的接口
 packet-filter 3002 outbound
```

---

## 十三、NAT（地址转换）详细配置

### 1. FW上配置源地址NAT（内网出公网）
```shell
acl number 2000
 rule permit ip source 10.0.0.0 ***********
interface GigabitEthernet0/0/3  # 公网出口接口
 nat outbound 2000
```

### 2. 静态NAT（DMZ服务器映射）
```shell
nat static global ********** inside *********
nat static global ********** inside *********
```

---

## 十四、防火墙安全策略详细示例

### 1. 基本安全区域划分
```shell
security-zone name trust
 import interface GigabitEthernet0/0/0  # 内网
security-zone name untrust
 import interface GigabitEthernet0/0/3  # 外网
security-zone name dmz
 import interface GigabitEthernet0/0/2  # DMZ
```

### 2. 策略规则
```shell
security-policy
 rule name allow_internet
  source-zone trust
  destination-zone untrust
  action permit
 rule name deny_inbound
  source-zone untrust
  destination-zone trust
  action deny
 rule name allow_dmz
  source-zone trust
  destination-zone dmz
  action permit
 rule name allow_dmz_out
  source-zone dmz
  destination-zone untrust
  action permit
```

---

## 十五、无线AC与AP详细配置

### 1. AC配置
```shell
wlan
 ssid campus_wifi
 security-profile abc123456
 ap-group 1
  ap-id 1
   ap-name AP1
   ap-ip *********
interface GigabitEthernet0/0/1
 port link-type trunk
 port trunk allow-pass vlan 50
wlan ac
 ap-group 1
```

### 2. AP配置
```shell
sysname AP1
interface WLAN-BSS1
 ssid campus_wifi
 security-profile abc123456
interface GigabitEthernet0/0/1
 port link-type access
 port default vlan 50
 ip address ********* *************
 undo shutdown
```

---

## 十六、接口分配与命名详细建议

### 1. AR1接口命名
```shell
interface GigabitEthernet0/0/1
 description 连接SW1-教学楼
interface GigabitEthernet0/0/2
 description 连接SW1-逸夫楼
interface GigabitEthernet0/0/3
 description 连接FW
interface GigabitEthernet0/0/4
 description 连接AP
```

### 2. SW1接口命名
```shell
interface GigabitEthernet0/0/1
 description 教学楼PC
interface GigabitEthernet0/0/2
 description 逸夫楼PC
interface GigabitEthernet0/0/3
 description 无线AP
interface GigabitEthernet0/0/24
 description 上联AR1
```

---

## 十七、常见问题与排查建议

- **VLAN不通**：检查trunk口允许VLAN、access口VLAN配置、端口状态。
- **OSPF邻居不建立**：检查接口IP、OSPF网络声明、接口up/down。
- **NAT不生效**：检查ACL匹配、NAT规则、出口接口配置。
- **端口安全误封**：如有终端更换，需清除sticky MAC。
- **ACL误拦截**：逐条排查ACL规则顺序和匹配范围。

---

## 十八、服务器与终端更详细配置建议

### 1. DNS服务器（DMZ）
- IP地址：*********/24
- 网关：********
- 建议配置防火墙仅允许特定端口（如53）访问

### 2. FTP服务器（DMZ）
- IP地址：*********/24
- 网关：********
- 建议配置防火墙仅允许特定端口（如21、20）访问

### 3. 终端PC（各VLAN）
- 教学楼PC：**********/24，网关*********
- 逸夫楼PC：**********/24, 网关*********
- 自习室PC：**********/24, 网关*********
- 阅览室PC：**********/24, 网关*********
- 无线终端：**********/24, 网关*********

---

## 网络拓扑树状结构示意

```text
校园网核心
│
├─ AR1（核心路由器1）
│   ├─ SW1（汇聚交换机1）
│   │   ├─ 教学楼PC（VLAN10）
│   │   ├─ 逸夫楼PC（VLAN20）
│   │   └─ AP（无线接入，VLAN50）
│   └─ FW（防火墙）
│
├─ AR2（核心路由器2）
│   └─ SW2（汇聚交换机2）
│       ├─ 自习室PC（VLAN30）
│       └─ 阅览室PC（VLAN40）
│
├─ FW（防火墙）
│   ├─ DMZ区
│   │   └─ Server-SW（服务器交换机）
│   │       ├─ DNS Server（VLAN60）
│   │       └─ FTP Server（VLAN60）
│   └─ ISP-R（互联网出口路由器）
│       └─ Internet-SW（互联网交换机）
│           └─ 互联网终端
│
├─ AC（无线控制器）
│   └─ AP（无线接入点，VLAN50）
│       └─ 无线终端
│
└─ 管理/业务VLAN（VLAN100/101）
    └─ 各设备管理口
```

---

### 说明

- 每个"─"代表一条物理或逻辑链路。
- 各VLAN已在括号中标注，便于对照配置。
- 你可根据实际eNSP设备命名和接口编号进一步细化。

---

如需更美观的ASCII图、Mermaid语法图、或Visio/Draw.io等格式，请告知你偏好的格式！

---

> 本文档已细化并补充更详细的配置，如需进一步定制或有特殊需求请继续补充说明。 