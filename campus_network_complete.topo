<?xml version="1.0" encoding="UNICODE" ?>
<topo version="1.3.00.100">
    <devices>
        <!-- 核心路由器AR1 -->
        <dev id="AR1-0001-0001-0001-000000000001" name="AR1" poe="0" model="AR2220" settings="" system_mac="00-E0-FC-01-01-01" com_port="2001" bootmode="1" cx="400.000000" cy="200.000000" edit_left="427" edit_top="254">
            <slot number="slot17" isMainBoard="1">
                <interface sztype="Ethernet" interfacename="GE" count="4" />
                <interface sztype="Ethernet" interfacename="Ethernet" count="2" />
            </slot>
        </dev>
        
        <!-- 核心路由器AR2 -->
        <dev id="AR2-0002-0002-0002-000000000002" name="AR2" poe="0" model="AR2220" settings="" system_mac="00-E0-FC-02-02-02" com_port="2002" bootmode="1" cx="600.000000" cy="200.000000" edit_left="627" edit_top="254">
            <slot number="slot17" isMainBoard="1">
                <interface sztype="Ethernet" interfacename="GE" count="4" />
                <interface sztype="Ethernet" interfacename="Ethernet" count="2" />
            </slot>
        </dev>
        
        <!-- 防火墙FW -->
        <dev id="FW01-0003-0003-0003-000000000003" name="FW" poe="0" model="USG6000V" settings="" system_mac="00-E0-FC-03-03-03" com_port="2003" bootmode="1" cx="500.000000" cy="350.000000" edit_left="527" edit_top="404">
            <slot number="slot17" isMainBoard="1">
                <interface sztype="Ethernet" interfacename="GE" count="8" />
            </slot>
        </dev>
        
        <!-- ISP路由器 -->
        <dev id="ISPR-0004-0004-0004-000000000004" name="ISP-R" poe="0" model="AR1220" settings="" system_mac="00-E0-FC-04-04-04" com_port="2004" bootmode="1" cx="500.000000" cy="500.000000" edit_left="527" edit_top="554">
            <slot number="slot17" isMainBoard="1">
                <interface sztype="Ethernet" interfacename="GE" count="2" />
                <interface sztype="Ethernet" interfacename="Ethernet" count="8" />
            </slot>
        </dev>
        
        <!-- 汇聚交换机SW1 -->
        <dev id="SW01-0005-0005-0005-000000000005" name="SW1" poe="0" model="S5700" settings="" system_mac="4C-1F-CC-01-01-01" com_port="2005" bootmode="1" cx="200.000000" cy="350.000000" edit_left="227" edit_top="404">
            <slot number="slot17" isMainBoard="1">
                <interface sztype="Ethernet" interfacename="GE" count="24" />
                <interface sztype="Ethernet" interfacename="10GE" count="4" />
            </slot>
        </dev>
        
        <!-- 汇聚交换机SW2 -->
        <dev id="SW02-0006-0006-0006-000000000006" name="SW2" poe="0" model="S5700" settings="" system_mac="4C-1F-CC-02-02-02" com_port="2006" bootmode="1" cx="800.000000" cy="350.000000" edit_left="827" edit_top="404">
            <slot number="slot17" isMainBoard="1">
                <interface sztype="Ethernet" interfacename="GE" count="24" />
                <interface sztype="Ethernet" interfacename="10GE" count="4" />
            </slot>
        </dev>
        
        <!-- 服务器交换机 -->
        <dev id="SVSW-0007-0007-0007-000000000007" name="Server-SW" poe="0" model="S3700" settings="" system_mac="4C-1F-CC-03-03-03" com_port="2007" bootmode="1" cx="350.000000" cy="500.000000" edit_left="377" edit_top="554">
            <slot number="slot17" isMainBoard="1">
                <interface sztype="Ethernet" interfacename="Ethernet" count="24" />
                <interface sztype="Ethernet" interfacename="GE" count="4" />
            </slot>
        </dev>
        
        <!-- 互联网交换机 -->
        <dev id="INSW-0008-0008-0008-000000000008" name="Internet-SW" poe="0" model="S3700" settings="" system_mac="4C-1F-CC-04-04-04" com_port="2008" bootmode="1" cx="650.000000" cy="500.000000" edit_left="677" edit_top="554">
            <slot number="slot17" isMainBoard="1">
                <interface sztype="Ethernet" interfacename="Ethernet" count="24" />
                <interface sztype="Ethernet" interfacename="GE" count="4" />
            </slot>
        </dev>
        
        <!-- 无线控制器AC -->
        <dev id="AC01-0009-0009-0009-000000000009" name="AC" poe="0" model="AC6005" settings="" system_mac="00-E0-FC-05-05-05" com_port="2009" bootmode="1" cx="100.000000" cy="450.000000" edit_left="127" edit_top="504">
            <slot number="slot17" isMainBoard="1">
                <interface sztype="Ethernet" interfacename="GE" count="2" />
            </slot>
        </dev>
        
        <!-- 无线接入点AP -->
        <dev id="AP01-0010-0010-0010-000000000010" name="AP" poe="0" model="AP2030DN" settings="" system_mac="00-E0-FC-06-06-06" com_port="2010" bootmode="1" cx="100.000000" cy="550.000000" edit_left="127" edit_top="604">
            <slot number="slot17" isMainBoard="1">
                <interface sztype="Ethernet" interfacename="GE" count="1" />
                <interface sztype="Ethernet" interfacename="WLAN-BSS" count="2" />
            </slot>
        </dev>
        
        <!-- 教学楼PC -->
        <dev id="PC01-0011-0011-0011-000000000011" name="Teaching-PC1" poe="0" model="PC" settings=" -simpc_ip ********** -simpc_mask ************* -simpc_gateway ********* -simpc_mac 50-50-50-10-10-01 -simpc_mc_dstip 0.0.0.0 -simpc_mc_dstmac 00-00-00-00-00-00 -simpc_dns1 ********* -simpc_dns2 ******* -simpc_ipv6 :: -simpc_prefix 128 -simpc_gatewayv6 :: -simpc_dhcp_state 0 -simpc_dhcpv6_state 0 -simpc_dns_auto_state 0 -simpc_igmp_version 1 -simpc_group_ip_start 0.0.0.0 -simpc_src_ip_start 0.0.0.0 -simpc_group_num 0 -simpc_group_step 0 -simpc_src_num 0 -simpc_src_step 0 -simpc_type MODE_IS_INCLUDE " system_mac="50-50-50-10-10-01" com_port="2011" bootmode="1" cx="50.000000" cy="250.000000" edit_left="77" edit_top="304">
            <slot number="slot17" isMainBoard="1">
                <interface sztype="Ethernet" interfacename="Ethernet" count="1" />
            </slot>
        </dev>
        
        <!-- 逸夫楼PC -->
        <dev id="PC02-0012-0012-0012-000000000012" name="Yifu-PC1" poe="0" model="PC" settings=" -simpc_ip ********** -simpc_mask ************* -simpc_gateway ********* -simpc_mac 50-50-50-20-20-01 -simpc_mc_dstip 0.0.0.0 -simpc_mc_dstmac 00-00-00-00-00-00 -simpc_dns1 ********* -simpc_dns2 ******* -simpc_ipv6 :: -simpc_prefix 128 -simpc_gatewayv6 :: -simpc_dhcp_state 0 -simpc_dhcpv6_state 0 -simpc_dns_auto_state 0 -simpc_igmp_version 1 -simpc_group_ip_start 0.0.0.0 -simpc_src_ip_start 0.0.0.0 -simpc_group_num 0 -simpc_group_step 0 -simpc_src_num 0 -simpc_src_step 0 -simpc_type MODE_IS_INCLUDE " system_mac="50-50-50-20-20-01" com_port="2012" bootmode="1" cx="150.000000" cy="250.000000" edit_left="177" edit_top="304">
            <slot number="slot17" isMainBoard="1">
                <interface sztype="Ethernet" interfacename="Ethernet" count="1" />
            </slot>
        </dev>
        
        <!-- 自习室PC -->
        <dev id="PC03-0013-0013-0013-000000000013" name="Study-PC1" poe="0" model="PC" settings=" -simpc_ip *********0 -simpc_mask ************* -simpc_gateway ********* -simpc_mac 50-50-50-30-30-01 -simpc_mc_dstip 0.0.0.0 -simpc_mc_dstmac 00-00-00-00-00-00 -simpc_dns1 ********* -simpc_dns2 ******* -simpc_ipv6 :: -simpc_prefix 128 -simpc_gatewayv6 :: -simpc_dhcp_state 0 -simpc_dhcpv6_state 0 -simpc_dns_auto_state 0 -simpc_igmp_version 1 -simpc_group_ip_start 0.0.0.0 -simpc_src_ip_start 0.0.0.0 -simpc_group_num 0 -simpc_group_step 0 -simpc_src_num 0 -simpc_src_step 0 -simpc_type MODE_IS_INCLUDE " system_mac="50-50-50-30-30-01" com_port="2013" bootmode="1" cx="850.000000" cy="250.000000" edit_left="877" edit_top="304">
            <slot number="slot17" isMainBoard="1">
                <interface sztype="Ethernet" interfacename="Ethernet" count="1" />
            </slot>
        </dev>
        
        <!-- 阅览室PC -->
        <dev id="PC04-0014-0014-0014-000000000014" name="Reading-PC1" poe="0" model="PC" settings=" -simpc_ip ********** -simpc_mask ************* -simpc_gateway ********* -simpc_mac 50-50-50-40-40-01 -simpc_mc_dstip 0.0.0.0 -simpc_mc_dstmac 00-00-00-00-00-00 -simpc_dns1 ********* -simpc_dns2 ******* -simpc_ipv6 :: -simpc_prefix 128 -simpc_gatewayv6 :: -simpc_dhcp_state 0 -simpc_dhcpv6_state 0 -simpc_dns_auto_state 0 -simpc_igmp_version 1 -simpc_group_ip_start 0.0.0.0 -simpc_src_ip_start 0.0.0.0 -simpc_group_num 0 -simpc_group_step 0 -simpc_src_num 0 -simpc_src_step 0 -simpc_type MODE_IS_INCLUDE " system_mac="50-50-50-40-40-01" com_port="2014" bootmode="1" cx="950.000000" cy="250.000000" edit_left="977" edit_top="304">
            <slot number="slot17" isMainBoard="1">
                <interface sztype="Ethernet" interfacename="Ethernet" count="1" />
            </slot>
        </dev>
        
        <!-- DNS服务器 -->
        <dev id="DNS1-0015-0015-0015-000000000015" name="DNS-Server" poe="0" model="PC" settings=" -simpc_ip ********* -simpc_mask ************* -simpc_gateway ******** -simpc_mac 50-50-50-01-01-10 -simpc_mc_dstip 0.0.0.0 -simpc_mc_dstmac 00-00-00-00-00-00 -simpc_dns1 127.0.0.1 -simpc_dns2 ******* -simpc_ipv6 :: -simpc_prefix 128 -simpc_gatewayv6 :: -simpc_dhcp_state 0 -simpc_dhcpv6_state 0 -simpc_dns_auto_state 0 -simpc_igmp_version 1 -simpc_group_ip_start 0.0.0.0 -simpc_src_ip_start 0.0.0.0 -simpc_group_num 0 -simpc_group_step 0 -simpc_src_num 0 -simpc_src_step 0 -simpc_type MODE_IS_INCLUDE " system_mac="50-50-50-01-01-10" com_port="2015" bootmode="1" cx="250.000000" cy="600.000000" edit_left="277" edit_top="654">
            <slot number="slot17" isMainBoard="1">
                <interface sztype="Ethernet" interfacename="Ethernet" count="1" />
            </slot>
        </dev>
        
        <!-- FTP服务器 -->
        <dev id="FTP1-0016-0016-0016-000000000016" name="FTP-Server" poe="0" model="PC" settings=" -simpc_ip ********* -simpc_mask ************* -simpc_gateway ******** -simpc_mac 50-50-50-01-01-20 -simpc_mc_dstip 0.0.0.0 -simpc_mc_dstmac 00-00-00-00-00-00 -simpc_dns1 ********* -simpc_dns2 ******* -simpc_ipv6 :: -simpc_prefix 128 -simpc_gatewayv6 :: -simpc_dhcp_state 0 -simpc_dhcpv6_state 0 -simpc_dns_auto_state 0 -simpc_igmp_version 1 -simpc_group_ip_start 0.0.0.0 -simpc_src_ip_start 0.0.0.0 -simpc_group_num 0 -simpc_group_step 0 -simpc_src_num 0 -simpc_src_step 0 -simpc_type MODE_IS_INCLUDE " system_mac="50-50-50-01-01-20" com_port="2016" bootmode="1" cx="450.000000" cy="600.000000" edit_left="477" edit_top="654">
            <slot number="slot17" isMainBoard="1">
                <interface sztype="Ethernet" interfacename="Ethernet" count="1" />
            </slot>
        </dev>
        
        <!-- 互联网终端 -->
        <dev id="INET-0017-0017-0017-000000000017" name="Internet-PC" poe="0" model="PC" settings=" -simpc_ip *********** -simpc_mask ************* -simpc_gateway ********* -simpc_mac 50-50-50-99-99-99 -simpc_mc_dstip 0.0.0.0 -simpc_mc_dstmac 00-00-00-00-00-00 -simpc_dns1 ******* -simpc_dns2 ******* -simpc_ipv6 :: -simpc_prefix 128 -simpc_gatewayv6 :: -simpc_dhcp_state 0 -simpc_dhcpv6_state 0 -simpc_dns_auto_state 0 -simpc_igmp_version 1 -simpc_group_ip_start 0.0.0.0 -simpc_src_ip_start 0.0.0.0 -simpc_group_num 0 -simpc_group_step 0 -simpc_src_num 0 -simpc_src_step 0 -simpc_type MODE_IS_INCLUDE " system_mac="50-50-50-99-99-99" com_port="2017" bootmode="1" cx="750.000000" cy="600.000000" edit_left="777" edit_top="654">
            <slot number="slot17" isMainBoard="1">
                <interface sztype="Ethernet" interfacename="Ethernet" count="1" />
            </slot>
        </dev>
    </devices>
    <lines>
        <!-- AR1 连接 SW1 (GE0/0/1 -> GE0/0/24) -->
        <line srcDeviceId="AR1-0001-0001-0001-000000000001" srcIndex="1" srcBoundRectIsMoved="0" srcBoundRect_X="427.000000" srcBoundRect_Y="254.000000" srcOffset_X="0.000000" srcOffset_Y="0.000000" tarDeviceId="SW01-0005-0005-0005-000000000005" tarIndex="23" tarBoundRectIsMoved="0" tarBoundRect_X="227.000000" tarBoundRect_Y="404.000000" tarOffset_X="0.000000" tarOffset_Y="0.000000" />

        <!-- AR2 连接 SW2 (GE0/0/1 -> GE0/0/24) -->
        <line srcDeviceId="AR2-0002-0002-0002-000000000002" srcIndex="1" srcBoundRectIsMoved="0" srcBoundRect_X="627.000000" srcBoundRect_Y="254.000000" srcOffset_X="0.000000" srcOffset_Y="0.000000" tarDeviceId="SW02-0006-0006-0006-000000000006" tarIndex="23" tarBoundRectIsMoved="0" tarBoundRect_X="827.000000" tarBoundRect_Y="404.000000" tarOffset_X="0.000000" tarOffset_Y="0.000000" />

        <!-- AR1 连接 FW (GE0/0/3 -> GE0/0/0) -->
        <line srcDeviceId="AR1-0001-0001-0001-000000000001" srcIndex="3" srcBoundRectIsMoved="0" srcBoundRect_X="427.000000" srcBoundRect_Y="254.000000" srcOffset_X="0.000000" srcOffset_Y="0.000000" tarDeviceId="FW01-0003-0003-0003-000000000003" tarIndex="0" tarBoundRectIsMoved="0" tarBoundRect_X="527.000000" tarBoundRect_Y="404.000000" tarOffset_X="0.000000" tarOffset_Y="0.000000" />

        <!-- AR2 连接 FW (GE0/0/3 -> GE0/0/1) -->
        <line srcDeviceId="AR2-0002-0002-0002-000000000002" srcIndex="3" srcBoundRectIsMoved="0" srcBoundRect_X="627.000000" srcBoundRect_Y="254.000000" srcOffset_X="0.000000" srcOffset_Y="0.000000" tarDeviceId="FW01-0003-0003-0003-000000000003" tarIndex="1" tarBoundRectIsMoved="0" tarBoundRect_X="527.000000" tarBoundRect_Y="404.000000" tarOffset_X="0.000000" tarOffset_Y="0.000000" />

        <!-- FW 连接 Server-SW (GE0/0/2 -> GE0/0/24) -->
        <line srcDeviceId="FW01-0003-0003-0003-000000000003" srcIndex="2" srcBoundRectIsMoved="0" srcBoundRect_X="527.000000" srcBoundRect_Y="404.000000" srcOffset_X="0.000000" srcOffset_Y="0.000000" tarDeviceId="SVSW-0007-0007-0007-000000000007" tarIndex="24" tarBoundRectIsMoved="0" tarBoundRect_X="377.000000" tarBoundRect_Y="554.000000" tarOffset_X="0.000000" tarOffset_Y="0.000000" />

        <!-- FW 连接 ISP-R (GE0/0/3 -> GE0/0/0) -->
        <line srcDeviceId="FW01-0003-0003-0003-000000000003" srcIndex="3" srcBoundRectIsMoved="0" srcBoundRect_X="527.000000" srcBoundRect_Y="404.000000" srcOffset_X="0.000000" srcOffset_Y="0.000000" tarDeviceId="ISPR-0004-0004-0004-000000000004" tarIndex="0" tarBoundRectIsMoved="0" tarBoundRect_X="527.000000" tarBoundRect_Y="554.000000" tarOffset_X="0.000000" tarOffset_Y="0.000000" />

        <!-- ISP-R 连接 Internet-SW (GE0/0/1 -> GE0/0/24) -->
        <line srcDeviceId="ISPR-0004-0004-0004-000000000004" srcIndex="1" srcBoundRectIsMoved="0" srcBoundRect_X="527.000000" srcBoundRect_Y="554.000000" srcOffset_X="0.000000" srcOffset_Y="0.000000" tarDeviceId="INSW-0008-0008-0008-000000000008" tarIndex="24" tarBoundRectIsMoved="0" tarBoundRect_X="677.000000" tarBoundRect_Y="554.000000" tarOffset_X="0.000000" tarOffset_Y="0.000000" />

        <!-- SW1 连接 AC (GE0/0/3 -> GE0/0/0) -->
        <line srcDeviceId="SW01-0005-0005-0005-000000000005" srcIndex="3" srcBoundRectIsMoved="0" srcBoundRect_X="227.000000" srcBoundRect_Y="404.000000" srcOffset_X="0.000000" srcOffset_Y="0.000000" tarDeviceId="AC01-0009-0009-0009-000000000009" tarIndex="0" tarBoundRectIsMoved="0" tarBoundRect_X="127.000000" tarBoundRect_Y="504.000000" tarOffset_X="0.000000" tarOffset_Y="0.000000" />

        <!-- AC 连接 AP (GE0/0/1 -> GE0/0/0) -->
        <line srcDeviceId="AC01-0009-0009-0009-000000000009" srcIndex="1" srcBoundRectIsMoved="0" srcBoundRect_X="127.000000" srcBoundRect_Y="504.000000" srcOffset_X="0.000000" srcOffset_Y="0.000000" tarDeviceId="AP01-0010-0010-0010-000000000010" tarIndex="0" tarBoundRectIsMoved="0" tarBoundRect_X="127.000000" tarBoundRect_Y="604.000000" tarOffset_X="0.000000" tarOffset_Y="0.000000" />

        <!-- Teaching-PC1 连接 SW1 (Eth0 -> GE0/0/1) -->
        <line srcDeviceId="PC01-0011-0011-0011-000000000011" srcIndex="0" srcBoundRectIsMoved="0" srcBoundRect_X="77.000000" srcBoundRect_Y="304.000000" srcOffset_X="0.000000" srcOffset_Y="0.000000" tarDeviceId="SW01-0005-0005-0005-000000000005" tarIndex="1" tarBoundRectIsMoved="0" tarBoundRect_X="227.000000" tarBoundRect_Y="404.000000" tarOffset_X="0.000000" tarOffset_Y="0.000000" />

        <!-- Yifu-PC1 连接 SW1 (Eth0 -> GE0/0/2) -->
        <line srcDeviceId="PC02-0012-0012-0012-000000000012" srcIndex="0" srcBoundRectIsMoved="0" srcBoundRect_X="177.000000" srcBoundRect_Y="304.000000" srcOffset_X="0.000000" srcOffset_Y="0.000000" tarDeviceId="SW01-0005-0005-0005-000000000005" tarIndex="2" tarBoundRectIsMoved="0" tarBoundRect_X="227.000000" tarBoundRect_Y="404.000000" tarOffset_X="0.000000" tarOffset_Y="0.000000" />

        <!-- Study-PC1 连接 SW2 (Eth0 -> GE0/0/1) -->
        <line srcDeviceId="PC03-0013-0013-0013-000000000013" srcIndex="0" srcBoundRectIsMoved="0" srcBoundRect_X="877.000000" srcBoundRect_Y="304.000000" srcOffset_X="0.000000" srcOffset_Y="0.000000" tarDeviceId="SW02-0006-0006-0006-000000000006" tarIndex="1" tarBoundRectIsMoved="0" tarBoundRect_X="827.000000" tarBoundRect_Y="404.000000" tarOffset_X="0.000000" tarOffset_Y="0.000000" />

        <!-- Reading-PC1 连接 SW2 (Eth0 -> GE0/0/2) -->
        <line srcDeviceId="PC04-0014-0014-0014-000000000014" srcIndex="0" srcBoundRectIsMoved="0" srcBoundRect_X="977.000000" srcBoundRect_Y="304.000000" srcOffset_X="0.000000" srcOffset_Y="0.000000" tarDeviceId="SW02-0006-0006-0006-000000000006" tarIndex="2" tarBoundRectIsMoved="0" tarBoundRect_X="827.000000" tarBoundRect_Y="404.000000" tarOffset_X="0.000000" tarOffset_Y="0.000000" />

        <!-- DNS-Server 连接 Server-SW (Eth0 -> GE0/0/1) -->
        <line srcDeviceId="DNS1-0015-0015-0015-000000000015" srcIndex="0" srcBoundRectIsMoved="0" srcBoundRect_X="277.000000" srcBoundRect_Y="654.000000" srcOffset_X="0.000000" srcOffset_Y="0.000000" tarDeviceId="SVSW-0007-0007-0007-000000000007" tarIndex="1" tarBoundRectIsMoved="0" tarBoundRect_X="377.000000" tarBoundRect_Y="554.000000" tarOffset_X="0.000000" tarOffset_Y="0.000000" />

        <!-- FTP-Server 连接 Server-SW (Eth0 -> GE0/0/2) -->
        <line srcDeviceId="FTP1-0016-0016-0016-000000000016" srcIndex="0" srcBoundRectIsMoved="0" srcBoundRect_X="477.000000" srcBoundRect_Y="654.000000" srcOffset_X="0.000000" srcOffset_Y="0.000000" tarDeviceId="SVSW-0007-0007-0007-000000000007" tarIndex="2" tarBoundRectIsMoved="0" tarBoundRect_X="377.000000" tarBoundRect_Y="554.000000" tarOffset_X="0.000000" tarOffset_Y="0.000000" />

        <!-- Internet-PC 连接 Internet-SW (Eth0 -> GE0/0/1) -->
        <line srcDeviceId="INET-0017-0017-0017-000000000017" srcIndex="0" srcBoundRectIsMoved="0" srcBoundRect_X="777.000000" srcBoundRect_Y="654.000000" srcOffset_X="0.000000" srcOffset_Y="0.000000" tarDeviceId="INSW-0008-0008-0008-000000000008" tarIndex="1" tarBoundRectIsMoved="0" tarBoundRect_X="677.000000" tarBoundRect_Y="554.000000" tarOffset_X="0.000000" tarOffset_Y="0.000000" />
    </lines>
    <shapes />
    <txttips />
</topo>
