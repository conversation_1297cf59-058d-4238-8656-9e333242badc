# eNSP网络自动化工具使用指南

## 📋 项目概述

本项目实现了基于eNSP的网络拓扑自动化生成和配置部署工具，支持从需求描述到网络部署的全流程自动化。

## 🎯 核心功能

### ✅ 已实现功能
- **拓扑自动生成**: 根据JSON需求自动生成eNSP拓扑文件
- **配置自动生成**: 基于模板自动生成华为设备配置
- **智能接口分配**: 根据连接类型智能分配设备接口
- **文件自动部署**: 自动将文件部署到eNSP目录
- **网络测试验证**: 自动化网络连通性测试

### 🔧 技术特性
- **模块化设计**: 各组件独立，易于维护和扩展
- **模板化配置**: 支持多种设备类型的配置模板
- **错误处理**: 完善的错误处理和日志记录
- **可扩展性**: 支持新设备类型和功能扩展

## 📁 文件结构

```
eNSP自动化工具/
├── ensp_automation.py          # 核心自动化模块
├── enhanced_automation.py      # 增强版自动化工具
├── config_templates.py         # 配置模板库
├── test_automation.py          # 测试脚本
├── demo_script.py              # 演示脚本
├── feasibility_analysis.md     # 可行性分析报告
├── campus_network_requirement.json  # 完整校园网需求示例
├── demo_requirement.json       # 演示需求文件
├── demo_campus_network.topo    # 演示拓扑文件
├── Core-R1.cfg                 # 路由器配置示例
├── Agg-SW1.cfg                 # 交换机配置示例
└── 使用指南.md                 # 本文件
```

## 🚀 快速开始

### 环境准备

#### 必需软件
- **Python 3.8+**: 运行自动化脚本
- **eNSP 1.3.00+**: 华为网络仿真平台
- **Windows 10/11**: 操作系统

#### Python依赖安装
```bash
pip install jinja2 requests pywinauto lxml
```

### 使用方法

#### 方法1: 手动部署（推荐新手）

1. **准备拓扑文件**
   - 将 `demo_campus_network.topo` 复制到: 
     `%USERPROFILE%\Documents\eNSP\topology\`

2. **准备配置文件**
   - 将 `Core-R1.cfg` 和 `Agg-SW1.cfg` 复制到:
     `%USERPROFILE%\AppData\Local\eNSP\config\`

3. **启动eNSP**
   - 打开eNSP软件
   - 文件 → 打开 → 选择拓扑文件
   - 启动所有设备

#### 方法2: 自动化部署（推荐高级用户）

```bash
# 运行增强版自动化工具
python enhanced_automation.py

# 或运行演示脚本
python demo_script.py
```

## 🏗️ 网络拓扑说明

### 拓扑结构
```
                    Internet
                        |
                   [Firewall]
                   /        \
            [Core-R1]    [Core-R2]
                |            |
           [Agg-SW1]    [Agg-SW2]
              |  |        |  |
           [PC1][PC2]  [PC3][PC4]
```

### IP地址规划

| 网段 | VLAN | 用途 | 网关 |
|------|------|------|------|
| *********/24 | 10 | 教学楼 | ********* |
| *********/24 | 20 | 办公楼 | ********* |
| *********/24 | 30 | 学生宿舍 | ********* |
| **********/24 | 100 | 管理网络 | ********** |
| **********/30 | - | 路由互联 | - |

### 设备角色

| 设备 | 型号 | 角色 | 管理IP |
|------|------|------|--------|
| Core-R1 | AR2220 | 核心路由器1 | ********** |
| Core-R2 | AR2220 | 核心路由器2 | ********** |
| Firewall | USG6000V | 安全网关 | ********** |
| Agg-SW1 | S5700 | 汇聚交换机1 | ********** |
| Agg-SW2 | S5700 | 汇聚交换机2 | ********** |

## 🔧 配置特性

### 路由器配置 (Core-R1)
- **VLAN**: 10, 20, 100
- **DHCP**: 为各VLAN提供DHCP服务
- **OSPF**: 动态路由协议
- **ACL**: 访问控制列表
- **NAT**: 网络地址转换

### 交换机配置 (Agg-SW1)
- **VLAN**: 10, 20, 100
- **端口安全**: MAC地址绑定
- **STP**: 生成树协议
- **DHCP Snooping**: DHCP安全
- **QoS**: 流量控制
- **端口镜像**: 网络监控

## 🧪 测试验证

### 连通性测试

1. **基础连通性**
   ```bash
   # 在PC1上测试
   ping *********    # 网关连通性
   ping *********    # 跨VLAN连通性
   ping *******      # 外网连通性
   ```

2. **路由表检查**
   ```bash
   # 在路由器上执行
   display ip routing-table
   display ospf peer
   display ospf lsdb
   ```

3. **VLAN验证**
   ```bash
   # 在交换机上执行
   display vlan
   display port vlan
   display mac-address
   ```

### 自动化测试

运行自动化测试脚本：
```bash
python test_automation.py
```

测试内容包括：
- 设备数据库功能
- 拓扑生成功能
- 配置模板功能
- 接口分配功能
- 文件部署功能

## 🛠️ 自定义配置

### 修改需求文件

编辑 `demo_requirement.json` 文件：

```json
{
  "topology_name": "my_network",
  "devices": [
    {
      "id": "1",
      "name": "MyRouter",
      "model": "AR2220",
      "type": "router"
    }
  ],
  "vlans": [
    {
      "id": 10,
      "description": "My VLAN",
      "network": "************/24"
    }
  ]
}
```

### 添加新设备类型

在 `ensp_automation.py` 中添加设备信息：

```python
"NEW_MODEL": DeviceInfo(
    model="NEW_MODEL",
    type="router",
    interfaces=[
        {"type": "GigabitEthernet", "count": 8, "prefix": "GigabitEthernet0/0/"}
    ],
    max_interfaces=8,
    supported_features=["ospf", "bgp", "nat"]
)
```

### 自定义配置模板

在 `config_templates.py` 中添加新模板：

```python
def _get_custom_template(self):
    return Template("""
    # 自定义配置模板
    sysname {{ device_name }}
    # 添加你的配置内容
    """)
```

## 🐛 故障排除

### 常见问题

| 问题 | 可能原因 | 解决方案 |
|------|----------|----------|
| eNSP启动失败 | 路径错误 | 检查eNSP安装路径 |
| 配置加载失败 | 语法错误 | 验证配置文件语法 |
| 设备启动慢 | 资源不足 | 增加等待时间或释放内存 |
| 网络不通 | 配置错误 | 检查IP地址和路由配置 |
| Python脚本报错 | 依赖缺失 | 安装所需Python包 |

### 调试方法

1. **查看日志文件**
   ```bash
   # 查看自动化日志
   type ensp_automation.log
   type enhanced_automation.log
   ```

2. **验证配置语法**
   ```bash
   # 在eNSP设备上执行
   display current-configuration
   ```

3. **检查接口状态**
   ```bash
   display interface brief
   display ip interface brief
   ```

## 📈 扩展功能

### 计划中的功能
- **Web界面**: 基于Flask的Web管理界面
- **配置备份**: 自动备份和版本控制
- **性能监控**: 网络性能实时监控
- **批量部署**: 支持多个网络同时部署
- **模板市场**: 预定义的网络模板库

### 贡献指南

欢迎贡献代码和建议：

1. Fork项目
2. 创建功能分支
3. 提交更改
4. 发起Pull Request

## 📞 技术支持

### 联系方式
- **项目地址**: [GitHub仓库链接]
- **问题反馈**: [Issues页面]
- **技术讨论**: [Discussions页面]

### 参考资料
- [华为eNSP官方文档](https://support.huawei.com/enterprise/zh/network-management/ensp-pid-9017384)
- [华为设备配置指南](https://support.huawei.com/enterprise/zh/doc/)
- [Python Jinja2文档](https://jinja.palletsprojects.com/)

---

**版本**: v1.0.0  
**更新时间**: 2024-12-19  
**作者**: eNSP自动化工具开发团队
