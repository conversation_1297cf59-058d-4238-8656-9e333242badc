{"topology_name": "campus_network_complete", "description": "完整的校园网拓扑，包含核心路由、汇聚交换、接入交换、防火墙、无线设备等", "devices": [{"id": "1", "name": "AR1", "model": "AR2220", "type": "router", "role": "core_router_1", "management_ip": "**********"}, {"id": "2", "name": "AR2", "model": "AR2220", "type": "router", "role": "core_router_2", "management_ip": "**********"}, {"id": "3", "name": "FW", "model": "USG6000V", "type": "firewall", "role": "security_gateway", "management_ip": "**********"}, {"id": "4", "name": "SW1", "model": "S5700", "type": "switch", "role": "aggregation_switch_1", "management_ip": "**********"}, {"id": "5", "name": "SW2", "model": "S5700", "type": "switch", "role": "aggregation_switch_2", "management_ip": "**********"}, {"id": "6", "name": "AC", "model": "AC6005", "type": "ac", "role": "wireless_controller", "management_ip": "**********"}, {"id": "7", "name": "AP1", "model": "AP2030DN", "type": "ap", "role": "wireless_access_point", "management_ip": "**********"}, {"id": "8", "name": "ISP-R", "model": "AR1220", "type": "router", "role": "isp_router", "management_ip": "*********"}], "vlans": [{"id": 10, "name": "teaching_building", "description": "Teaching Building VLAN", "network": "*********/24", "gateway": "*********"}, {"id": 20, "name": "yifu_building", "description": "Yifu Building VLAN", "network": "*********/24", "gateway": "*********"}, {"id": 30, "name": "study_room", "description": "Study Room VLAN", "network": "*********/24", "gateway": "*********"}, {"id": 40, "name": "reading_room", "description": "Reading Room VLAN", "network": "*********/24", "gateway": "*********"}, {"id": 50, "name": "wireless", "description": "Wireless VLAN", "network": "*********/24", "gateway": "*********"}, {"id": 60, "name": "dmz", "description": "DMZ VLAN", "network": "********/24", "gateway": "********"}, {"id": 100, "name": "management", "description": "Management VLAN", "network": "**********/24", "gateway": "**********"}], "routing_protocol": "ospf", "security_features": ["acl", "nat", "firewall", "port_security"], "connections": [{"description": "AR1 to SW1", "src_device": "1", "dst_device": "4", "src_model": "AR2220", "dst_model": "S5700", "link_type": "trunk"}, {"description": "AR2 to SW2", "src_device": "2", "dst_device": "5", "src_model": "AR2220", "dst_model": "S5700", "link_type": "trunk"}, {"description": "AR1 to FW", "src_device": "1", "dst_device": "3", "src_model": "AR2220", "dst_model": "USG6000V", "link_type": "routed"}, {"description": "AR2 to FW", "src_device": "2", "dst_device": "3", "src_model": "AR2220", "dst_model": "USG6000V", "link_type": "routed"}, {"description": "FW to ISP-R", "src_device": "3", "dst_device": "8", "src_model": "USG6000V", "dst_model": "AR1220", "link_type": "routed"}, {"description": "SW1 to AC", "src_device": "4", "dst_device": "6", "src_model": "S5700", "dst_model": "AC6005", "link_type": "access"}, {"description": "SW1 to AP1", "src_device": "4", "dst_device": "7", "src_model": "S5700", "dst_model": "AP2030DN", "link_type": "access"}], "device_configs": {"AR1": {"device_name": "AR1", "timestamp": "2024-01-01 00:00:00", "timezone": "China-Standard-Time", "admin_password": "<PERSON><PERSON><PERSON>@123", "snmp_enabled": true, "snmp_community": "public", "vlans": [{"id": 10, "description": "Teaching Building"}, {"id": 20, "description": "Yifu Building"}, {"id": 50, "description": "Wireless"}, {"id": 100, "description": "Management"}], "interfaces": [{"name": "GigabitEthernet0/0/1", "description": "To SW1 - Trunk", "link_type": "trunk", "vlans": [10, 20, 50, 100]}, {"name": "GigabitEthernet0/0/2", "description": "To FW - Routed", "ip": "**********", "mask": "***************"}, {"name": "Vlanif10", "description": "Teaching Building Gateway", "ip": "*********", "mask": "*************", "dhcp_enable": true}, {"name": "Vlanif20", "description": "Yifu Building Gateway", "ip": "*********", "mask": "*************", "dhcp_enable": true}, {"name": "Vlanif50", "description": "Wireless Gateway", "ip": "*********", "mask": "*************", "dhcp_enable": true}, {"name": "Vlanif100", "description": "Management Gateway", "ip": "**********", "mask": "*************"}], "dhcp_pools": [{"name": "teaching_pool", "network": "*********", "mask": "*************", "gateway": "*********", "dns": ["*******", "***************"], "lease_days": 1}, {"name": "yifu_pool", "network": "*********", "mask": "*************", "gateway": "*********", "dns": ["*******", "***************"], "lease_days": 1}, {"name": "wireless_pool", "network": "*********", "mask": "*************", "gateway": "*********", "dns": ["*******", "***************"], "lease_days": 1}], "static_routes": [{"destination": "0.0.0.0", "mask": "0.0.0.0", "next_hop": "**********"}], "acls": [{"number": 3000, "description": "Block Teaching to DMZ", "rules": [{"id": 5, "action": "deny", "protocol": "ip", "source": "********* *********", "destination": "******** *********"}, {"id": 10, "action": "permit", "protocol": "ip"}]}]}}, "test_config": {"startup_wait": 90, "test_ips": ["*********", "*********", "*********", "*********", "*********", "**********"], "connectivity_tests": [{"name": "Internal VLAN Connectivity", "source": "*********0", "targets": ["*********0", "**********"]}, {"name": "Internet Connectivity", "source": "*********0", "targets": ["*******"]}]}}