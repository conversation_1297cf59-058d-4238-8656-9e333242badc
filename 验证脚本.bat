@echo off
chcp 65001 >nul
echo ========================================
echo eNSP拓扑文件验证和部署脚本
echo ========================================
echo.

:: 检查当前目录文件
echo 🔍 检查当前目录文件...
if exist "real_format_demo.topo" (
    echo ✅ 找到拓扑文件: real_format_demo.topo
) else (
    echo ❌ 未找到拓扑文件: real_format_demo.topo
    goto :error
)

if exist "R1.cfg" (
    echo ✅ 找到配置文件: R1.cfg
) else (
    echo ❌ 未找到配置文件: R1.cfg
    goto :error
)

if exist "SW1.cfg" (
    echo ✅ 找到配置文件: SW1.cfg
) else (
    echo ❌ 未找到配置文件: SW1.cfg
    goto :error
)

if exist "SW2.cfg" (
    echo ✅ 找到配置文件: SW2.cfg
) else (
    echo ❌ 未找到配置文件: SW2.cfg
    goto :error
)

echo.
echo 📁 创建eNSP目录...

:: 创建拓扑目录
set "TOPO_DIR=%USERPROFILE%\Documents\eNSP\topology"
if not exist "%TOPO_DIR%" (
    mkdir "%TOPO_DIR%"
    echo ✅ 创建拓扑目录: %TOPO_DIR%
) else (
    echo ✅ 拓扑目录已存在: %TOPO_DIR%
)

:: 创建配置目录
set "CONFIG_DIR=%USERPROFILE%\AppData\Local\eNSP\config"
if not exist "%CONFIG_DIR%" (
    mkdir "%CONFIG_DIR%"
    echo ✅ 创建配置目录: %CONFIG_DIR%
) else (
    echo ✅ 配置目录已存在: %CONFIG_DIR%
)

echo.
echo 📋 复制文件...

:: 复制拓扑文件
copy "simple_demo.topo" "%TOPO_DIR%\" >nul
if %errorlevel% equ 0 (
    echo ✅ 拓扑文件复制成功
) else (
    echo ❌ 拓扑文件复制失败
    goto :error
)

:: 复制配置文件
copy "R1.cfg" "%CONFIG_DIR%\" >nul
copy "SW1.cfg" "%CONFIG_DIR%\" >nul  
copy "SW2.cfg" "%CONFIG_DIR%\" >nul
if %errorlevel% equ 0 (
    echo ✅ 配置文件复制成功
) else (
    echo ❌ 配置文件复制失败
    goto :error
)

echo.
echo 🔍 验证XML格式...
findstr /C:"<?xml" simple_demo.topo >nul
if %errorlevel% equ 0 (
    echo ✅ XML头部格式正确
) else (
    echo ❌ XML头部格式错误
    goto :error
)

findstr /C:"</topo>" simple_demo.topo >nul
if %errorlevel% equ 0 (
    echo ✅ XML结构完整
) else (
    echo ❌ XML结构不完整
    goto :error
)

echo.
echo 🎉 部署完成！
echo.
echo 📋 下一步操作：
echo 1. 启动eNSP软件
echo 2. 文件 → 打开 → 选择 simple_demo.topo
echo 3. 右键设备 → 启动（建议先启动R1，再启动交换机）
echo 4. 双击设备进入命令行验证配置
echo.
echo 💡 测试建议：
echo - PC1 IP: *************/24, 网关: ************
echo - PC3 IP: *************/24, 网关: ************
echo - 测试: ping ************ 和 ping *************
echo.
echo 📖 如遇问题请查看: 故障排除指南.md
echo.
pause
goto :end

:error
echo.
echo ❌ 部署失败！请检查：
echo 1. 确保所有文件都在当前目录
echo 2. 检查文件权限
echo 3. 确保eNSP已正确安装
echo.
pause

:end
