{"topology_name": "demo_campus_network", "description": "演示用校园网拓扑", "devices": [{"id": "1", "name": "Core-R1", "model": "AR2220", "type": "router", "role": "核心路由器1", "management_ip": "**********"}, {"id": "2", "name": "Core-R2", "model": "AR2220", "type": "router", "role": "核心路由器2", "management_ip": "**********"}, {"id": "3", "name": "Firewall", "model": "USG6000V", "type": "firewall", "role": "安全网关", "management_ip": "**********"}, {"id": "4", "name": "Agg-SW1", "model": "S5700", "type": "switch", "role": "汇聚交换机1", "management_ip": "**********"}, {"id": "5", "name": "Agg-SW2", "model": "S5700", "type": "switch", "role": "汇聚交换机2", "management_ip": "**********"}], "vlans": [{"id": 10, "name": "teaching", "description": "教学楼VLAN", "network": "*********/24", "gateway": "*********"}, {"id": 20, "name": "office", "description": "办公楼VLAN", "network": "*********/24", "gateway": "*********"}, {"id": 30, "name": "student", "description": "学生宿舍VLAN", "network": "*********/24", "gateway": "*********"}, {"id": 100, "name": "management", "description": "管理VLAN", "network": "**********/24", "gateway": "**********"}], "routing_protocol": "ospf", "security_features": ["acl", "nat", "firewall"], "connections": [{"description": "Core-R1 连接 Agg-SW1", "src_device": "1", "dst_device": "4", "link_type": "trunk"}, {"description": "Core-R2 连接 Agg-SW2", "src_device": "2", "dst_device": "5", "link_type": "trunk"}, {"description": "Core-R1 连接 Firewall", "src_device": "1", "dst_device": "3", "link_type": "routed"}, {"description": "Core-R2 连接 Firewall", "src_device": "2", "dst_device": "3", "link_type": "routed"}], "device_configs": {"Core-R1": {"device_name": "Core-R1", "vlans": [{"id": 10, "description": "Teaching Building"}, {"id": 20, "description": "Office Building"}, {"id": 100, "description": "Management"}], "interfaces": [{"name": "GigabitEthernet0/0/0", "description": "To Agg-SW1 - Trunk", "link_type": "trunk", "vlans": [10, 20, 100]}, {"name": "GigabitEthernet0/0/1", "description": "To Firewall - Routed", "ip": "**********", "mask": "***************"}, {"name": "Vlanif10", "description": "Teaching Building Gateway", "ip": "*********", "mask": "*************", "dhcp_enable": true}, {"name": "Vlanif20", "description": "Office Building Gateway", "ip": "*********", "mask": "*************", "dhcp_enable": true}, {"name": "Vlanif100", "description": "Management Gateway", "ip": "**********", "mask": "*************"}], "dhcp_pools": [{"name": "teaching_pool", "network": "*********", "mask": "*************", "gateway": "*********", "dns": ["*******", "***************"], "lease_days": 1}, {"name": "office_pool", "network": "*********", "mask": "*************", "gateway": "*********", "dns": ["*******", "***************"], "lease_days": 1}], "static_routes": [{"destination": "0.0.0.0", "mask": "0.0.0.0", "next_hop": "**********"}], "acls": [{"number": 3000, "description": "Block Teaching to Management", "rules": [{"id": 5, "action": "deny", "protocol": "ip", "source": "********* *********", "destination": "********** *********"}, {"id": 10, "action": "permit", "protocol": "ip"}]}]}}, "test_config": {"startup_wait": 90, "test_ips": ["*********", "*********", "**********"], "connectivity_tests": [{"name": "Internal VLAN Connectivity", "source": "*********0", "targets": ["*********0"]}, {"name": "Management Access", "source": "**********0", "targets": ["*********", "*********"]}]}}