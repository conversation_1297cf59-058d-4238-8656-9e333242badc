<?xml version="1.0" encoding="UTF-8"?>
<topo version="1.3.00.100">
    <devices>
        <device id="1" name="Core-R1" poe="false" model="AR2220" x="200" y="100" label_x="200" label_y="70">
            <interface id="0" name="GigabitEthernet0/0/0" />
            <interface id="1" name="GigabitEthernet0/0/1" />
            <interface id="2" name="GigabitEthernet0/0/2" />
            <interface id="3" name="GigabitEthernet0/0/3" />
        </device>
        <device id="2" name="Core-R2" poe="false" model="AR2220" x="400" y="100" label_x="400" label_y="70">
            <interface id="0" name="GigabitEthernet0/0/0" />
            <interface id="1" name="GigabitEthernet0/0/1" />
            <interface id="2" name="GigabitEthernet0/0/2" />
            <interface id="3" name="GigabitEthernet0/0/3" />
        </device>
        <device id="3" name="Firewall" poe="false" model="USG6000V" x="300" y="200" label_x="300" label_y="170">
            <interface id="0" name="GigabitEthernet1/0/0" />
            <interface id="1" name="GigabitEthernet1/0/1" />
            <interface id="2" name="GigabitEthernet1/0/2" />
            <interface id="3" name="GigabitEthernet1/0/3" />
        </device>
        <device id="4" name="Agg-SW1" poe="false" model="S5700" x="100" y="300" label_x="100" label_y="270">
            <interface id="0" name="GigabitEthernet0/0/1" />
            <interface id="1" name="GigabitEthernet0/0/2" />
            <interface id="2" name="GigabitEthernet0/0/3" />
            <interface id="3" name="GigabitEthernet0/0/4" />
            <interface id="4" name="GigabitEthernet0/0/5" />
            <interface id="5" name="GigabitEthernet0/0/6" />
            <interface id="6" name="GigabitEthernet0/0/7" />
            <interface id="7" name="GigabitEthernet0/0/8" />
            <interface id="8" name="GigabitEthernet0/0/9" />
            <interface id="9" name="GigabitEthernet0/0/10" />
            <interface id="10" name="GigabitEthernet0/0/11" />
            <interface id="11" name="GigabitEthernet0/0/12" />
            <interface id="12" name="GigabitEthernet0/0/13" />
            <interface id="13" name="GigabitEthernet0/0/14" />
            <interface id="14" name="GigabitEthernet0/0/15" />
            <interface id="15" name="GigabitEthernet0/0/16" />
            <interface id="16" name="GigabitEthernet0/0/17" />
            <interface id="17" name="GigabitEthernet0/0/18" />
            <interface id="18" name="GigabitEthernet0/0/19" />
            <interface id="19" name="GigabitEthernet0/0/20" />
            <interface id="20" name="GigabitEthernet0/0/21" />
            <interface id="21" name="GigabitEthernet0/0/22" />
            <interface id="22" name="GigabitEthernet0/0/23" />
            <interface id="23" name="GigabitEthernet0/0/24" />
        </device>
        <device id="5" name="Agg-SW2" poe="false" model="S5700" x="500" y="300" label_x="500" label_y="270">
            <interface id="0" name="GigabitEthernet0/0/1" />
            <interface id="1" name="GigabitEthernet0/0/2" />
            <interface id="2" name="GigabitEthernet0/0/3" />
            <interface id="3" name="GigabitEthernet0/0/4" />
            <interface id="4" name="GigabitEthernet0/0/5" />
            <interface id="5" name="GigabitEthernet0/0/6" />
            <interface id="6" name="GigabitEthernet0/0/7" />
            <interface id="7" name="GigabitEthernet0/0/8" />
            <interface id="8" name="GigabitEthernet0/0/9" />
            <interface id="9" name="GigabitEthernet0/0/10" />
            <interface id="10" name="GigabitEthernet0/0/11" />
            <interface id="11" name="GigabitEthernet0/0/12" />
            <interface id="12" name="GigabitEthernet0/0/13" />
            <interface id="13" name="GigabitEthernet0/0/14" />
            <interface id="14" name="GigabitEthernet0/0/15" />
            <interface id="15" name="GigabitEthernet0/0/16" />
            <interface id="16" name="GigabitEthernet0/0/17" />
            <interface id="17" name="GigabitEthernet0/0/18" />
            <interface id="18" name="GigabitEthernet0/0/19" />
            <interface id="19" name="GigabitEthernet0/0/20" />
            <interface id="20" name="GigabitEthernet0/0/21" />
            <interface id="21" name="GigabitEthernet0/0/22" />
            <interface id="22" name="GigabitEthernet0/0/23" />
            <interface id="23" name="GigabitEthernet0/0/24" />
        </device>
        <device id="6" name="PC1" poe="false" model="PC" x="50" y="400" label_x="50" label_y="370">
            <interface id="0" name="Ethernet0/0/1" />
        </device>
        <device id="7" name="PC2" poe="false" model="PC" x="150" y="400" label_x="150" label_y="370">
            <interface id="0" name="Ethernet0/0/1" />
        </device>
        <device id="8" name="PC3" poe="false" model="PC" x="450" y="400" label_x="450" label_y="370">
            <interface id="0" name="Ethernet0/0/1" />
        </device>
        <device id="9" name="PC4" poe="false" model="PC" x="550" y="400" label_x="550" label_y="370">
            <interface id="0" name="Ethernet0/0/1" />
        </device>
    </devices>
    <lines>
        <!-- Core-R1 to Agg-SW1 -->
        <line srcDeviceId="1" srcInterfaceId="0" dstDeviceId="4" dstInterfaceId="23" />
        <!-- Core-R2 to Agg-SW2 -->
        <line srcDeviceId="2" srcInterfaceId="0" dstDeviceId="5" dstInterfaceId="23" />
        <!-- Core-R1 to Firewall -->
        <line srcDeviceId="1" srcInterfaceId="1" dstDeviceId="3" dstInterfaceId="0" />
        <!-- Core-R2 to Firewall -->
        <line srcDeviceId="2" srcInterfaceId="1" dstDeviceId="3" dstInterfaceId="1" />
        <!-- PC1 to Agg-SW1 -->
        <line srcDeviceId="6" srcInterfaceId="0" dstDeviceId="4" dstInterfaceId="0" />
        <!-- PC2 to Agg-SW1 -->
        <line srcDeviceId="7" srcInterfaceId="0" dstDeviceId="4" dstInterfaceId="1" />
        <!-- PC3 to Agg-SW2 -->
        <line srcDeviceId="8" srcInterfaceId="0" dstDeviceId="5" dstInterfaceId="0" />
        <!-- PC4 to Agg-SW2 -->
        <line srcDeviceId="9" srcInterfaceId="0" dstDeviceId="5" dstInterfaceId="1" />
    </lines>
</topo>
